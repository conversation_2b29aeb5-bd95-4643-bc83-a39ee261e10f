<template>
  <div class="register-container">
    <div class="register-card">
      <!-- 头部 -->
      <div class="register-header">
        <div class="logo">
          <i class="fas fa-paw"></i>
          <h1>宠物领养中心</h1>
        </div>
        <p class="subtitle">创建您的账户</p>
      </div>

      <!-- 注册表单 -->
      <el-form
        ref="registerFormRef"
        :model="registerForm"
        :rules="registerRules"
        class="register-form"
        @submit.prevent="handleRegister"
      >
        <el-form-item prop="username">
          <el-input
            v-model="registerForm.username"
            placeholder="请输入用户名"
            size="large"
            :prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item prop="email">
          <el-input
            v-model="registerForm.email"
            placeholder="请输入邮箱地址"
            size="large"
            :prefix-icon="Message"
            clearable
          />
        </el-form-item>

        <el-form-item prop="phone">
          <el-input
            v-model="registerForm.phone"
            placeholder="请输入手机号"
            size="large"
            :prefix-icon="Phone"
            clearable
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="registerForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            :prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>

        <el-form-item prop="confirmPassword">
          <el-input
            v-model="registerForm.confirmPassword"
            type="password"
            placeholder="请确认密码"
            size="large"
            :prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleRegister"
          />
        </el-form-item>

        <el-form-item prop="agreement">
          <el-checkbox v-model="registerForm.agreement">
            我已阅读并同意
            <el-link type="primary" @click="showAgreement">
              《用户协议》
            </el-link>
            和
            <el-link type="primary" @click="showPrivacy">
              《隐私政策》
            </el-link>
          </el-checkbox>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="register-btn"
            :loading="authStore.loading"
            @click="handleRegister"
          >
            <i class="fas fa-user-plus"></i>
            注册
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 登录链接 -->
      <div class="login-link">
        <span>已有账户？</span>
        <router-link to="/login" class="link">立即登录</router-link>
      </div>
    </div>

    <!-- 注册优势 -->
    <div class="register-benefits">
      <h2>加入我们，开启爱心之旅</h2>
      <div class="benefits-list">
        <div class="benefit-item">
          <div class="benefit-icon">
            <i class="fas fa-heart"></i>
          </div>
          <div class="benefit-content">
            <h3>爱心领养</h3>
            <p>为流浪宠物寻找温暖的家</p>
          </div>
        </div>
        <div class="benefit-item">
          <div class="benefit-icon">
            <i class="fas fa-shield-alt"></i>
          </div>
          <div class="benefit-content">
            <h3>安全保障</h3>
            <p>严格的审核流程确保安全</p>
          </div>
        </div>
        <div class="benefit-item">
          <div class="benefit-icon">
            <i class="fas fa-users"></i>
          </div>
          <div class="benefit-content">
            <h3>社区支持</h3>
            <p>专业的养宠指导和社区</p>
          </div>
        </div>
        <div class="benefit-item">
          <div class="benefit-icon">
            <i class="fas fa-mobile-alt"></i>
          </div>
          <div class="benefit-content">
            <h3>便捷服务</h3>
            <p>随时随地管理您的申请</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-item item-1">
        <i class="fas fa-paw"></i>
      </div>
      <div class="decoration-item item-2">
        <i class="fas fa-heart"></i>
      </div>
      <div class="decoration-item item-3">
        <i class="fas fa-dog"></i>
      </div>
      <div class="decoration-item item-4">
        <i class="fas fa-cat"></i>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, Message, Phone, Lock } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { validateEmail, validatePhone } from '@/utils'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 表单引用
const registerFormRef = ref()

// 注册表单数据
const registerForm = reactive({
  username: '',
  email: '',
  phone: '',
  password: '',
  confirmPassword: '',
  agreement: false
})

// 自定义验证器
const validateUsername = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入用户名'))
  } else if (value.length < 3 || value.length > 20) {
    callback(new Error('用户名长度在 3 到 20 个字符'))
  } else if (!/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/.test(value)) {
    callback(new Error('用户名只能包含字母、数字、下划线和中文'))
  } else {
    callback()
  }
}

const validateEmailFormat = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入邮箱地址'))
  } else if (!validateEmail(value)) {
    callback(new Error('请输入有效的邮箱地址'))
  } else {
    callback()
  }
}

const validatePhoneFormat = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入手机号'))
  } else if (!validatePhone(value)) {
    callback(new Error('请输入有效的手机号'))
  } else {
    callback()
  }
}

const validatePasswordConfirm = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请确认密码'))
  } else if (value !== registerForm.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

const validateAgreement = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请阅读并同意用户协议和隐私政策'))
  } else {
    callback()
  }
}

// 表单验证规则
const registerRules = {
  username: [
    { validator: validateUsername, trigger: 'blur' }
  ],
  email: [
    { validator: validateEmailFormat, trigger: 'blur' }
  ],
  phone: [
    { validator: validatePhoneFormat, trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' },
    { pattern: /^(?=.*[a-zA-Z])(?=.*\d)/, message: '密码必须包含字母和数字', trigger: 'blur' }
  ],
  confirmPassword: [
    { validator: validatePasswordConfirm, trigger: 'blur' }
  ],
  agreement: [
    { validator: validateAgreement, trigger: 'change' }
  ]
}

// 处理注册
const handleRegister = async () => {
  if (!registerFormRef.value) return
  
  try {
    await registerFormRef.value.validate()
    
    const { confirmPassword, agreement, ...userData } = registerForm
    
    const result = await authStore.register(userData)
    
    if (result.success) {
      ElMessage.success('注册成功！请登录您的账户')
      router.push('/login')
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    console.error('注册表单验证失败:', error)
  }
}

// 显示用户协议
const showAgreement = () => {
  ElMessageBox.alert(
    '这里是用户协议的内容...',
    '用户协议',
    {
      confirmButtonText: '我已阅读',
      type: 'info'
    }
  )
}

// 显示隐私政策
const showPrivacy = () => {
  ElMessageBox.alert(
    '这里是隐私政策的内容...',
    '隐私政策',
    {
      confirmButtonText: '我已阅读',
      type: 'info'
    }
  )
}

// 页面挂载时的处理
onMounted(() => {
  // 如果已经登录，直接跳转
  if (authStore.isAuthenticated) {
    router.push('/')
  }
})
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
  position: relative;
  overflow: hidden;
  gap: 3rem;
}

.register-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 3rem;
  width: 100%;
  max-width: 400px;
  position: relative;
  z-index: 2;
}

.register-header {
  text-align: center;
  margin-bottom: 2rem;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 1rem;
}

.logo i {
  font-size: 2rem;
  color: var(--accent);
}

.logo h1 {
  font-size: 1.5rem;
  color: var(--secondary);
  margin: 0;
}

.subtitle {
  color: #666;
  font-size: 1rem;
  margin: 0;
}

.register-form {
  margin-bottom: 1.5rem;
}

.register-btn {
  width: 100%;
  height: 48px;
  font-size: 1rem;
  font-weight: 600;
}

.login-link {
  text-align: center;
  color: #666;
  font-size: 0.9rem;
}

.link {
  color: var(--primary);
  text-decoration: none;
  font-weight: 500;
  margin-left: 0.5rem;
}

.link:hover {
  text-decoration: underline;
}

.register-benefits {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 2rem;
  max-width: 400px;
  position: relative;
  z-index: 2;
}

.register-benefits h2 {
  text-align: center;
  color: var(--secondary);
  margin-bottom: 2rem;
  font-size: 1.3rem;
}

.benefits-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.benefit-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.benefit-content h3 {
  margin: 0 0 0.5rem 0;
  color: var(--secondary);
  font-size: 1rem;
}

.benefit-content p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.decoration-item {
  position: absolute;
  color: rgba(255, 255, 255, 0.1);
  font-size: 3rem;
  animation: float 6s ease-in-out infinite;
}

.item-1 {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.item-2 {
  top: 20%;
  right: 15%;
  animation-delay: 1.5s;
}

.item-3 {
  bottom: 20%;
  left: 15%;
  animation-delay: 3s;
}

.item-4 {
  bottom: 10%;
  right: 10%;
  animation-delay: 4.5s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .register-container {
    flex-direction: column;
    gap: 2rem;
  }
  
  .register-benefits {
    max-width: 400px;
  }
}

@media (max-width: 480px) {
  .register-container {
    padding: 1rem;
  }
  
  .register-card,
  .register-benefits {
    padding: 2rem;
  }
  
  .benefits-list {
    gap: 1rem;
  }
  
  .benefit-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
}
</style>
