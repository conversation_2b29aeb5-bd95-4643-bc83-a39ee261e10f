<template>
  <div class="login-container">
    <div class="login-card">
      <!-- 头部 -->
      <div class="login-header">
        <div class="logo">
          <i class="fas fa-paw"></i>
          <h1>宠物领养中心</h1>
        </div>
        <p class="subtitle">登录您的账户</p>
      </div>

      <!-- 登录表单 -->
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名或邮箱"
            size="large"
            :prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            :prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <el-form-item>
          <div class="form-options">
            <el-checkbox v-model="rememberMe">记住我</el-checkbox>
            <el-link type="primary" @click="showForgotPassword">
              忘记密码？
            </el-link>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="login-btn"
            :loading="authStore.loading"
            @click="handleLogin"
          >
            <i class="fas fa-sign-in-alt"></i>
            登录
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 分割线 -->
      <div class="divider">
        <span>或</span>
      </div>

      <!-- 快速登录 -->
      <div class="quick-login">
        <p class="quick-login-title">快速体验</p>
        <div class="demo-accounts">
          <el-button
            type="info"
            plain
            size="small"
            @click="quickLogin('admin')"
            :loading="authStore.loading"
          >
            管理员登录
          </el-button>
          <el-button
            type="success"
            plain
            size="small"
            @click="quickLogin('user')"
            :loading="authStore.loading"
          >
            普通用户登录
          </el-button>
        </div>
      </div>

      <!-- 注册链接 -->
      <div class="register-link">
        <span>还没有账户？</span>
        <router-link to="/register" class="link">立即注册</router-link>
      </div>
    </div>

    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-item item-1">
        <i class="fas fa-paw"></i>
      </div>
      <div class="decoration-item item-2">
        <i class="fas fa-heart"></i>
      </div>
      <div class="decoration-item item-3">
        <i class="fas fa-dog"></i>
      </div>
      <div class="decoration-item item-4">
        <i class="fas fa-cat"></i>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { validateEmail } from '@/utils'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 表单引用
const loginFormRef = ref()

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 记住我
const rememberMe = ref(false)

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名或邮箱', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    await loginFormRef.value.validate()
    
    const result = await authStore.login(loginForm)
    
    if (result.success) {
      ElMessage.success('登录成功！')
      
      // 跳转到目标页面或首页
      const redirect = route.query.redirect || '/'
      router.push(redirect)
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    console.error('登录表单验证失败:', error)
  }
}

// 快速登录（演示用）
const quickLogin = async (type) => {
  const demoAccounts = {
    admin: { username: 'admin', password: 'admin123' },
    user: { username: 'user1', password: 'user1123' }
  }
  
  const account = demoAccounts[type]
  if (!account) return
  
  loginForm.username = account.username
  loginForm.password = account.password
  
  await handleLogin()
}

// 显示忘记密码对话框
const showForgotPassword = () => {
  ElMessageBox.prompt('请输入您的邮箱地址', '找回密码', {
    confirmButtonText: '发送重置邮件',
    cancelButtonText: '取消',
    inputPattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    inputErrorMessage: '请输入有效的邮箱地址'
  }).then(({ value }) => {
    ElMessage.success(`重置密码邮件已发送到 ${value}`)
  }).catch(() => {
    // 用户取消
  })
}

// 页面挂载时的处理
onMounted(() => {
  // 如果已经登录，直接跳转
  if (authStore.isAuthenticated) {
    const redirect = route.query.redirect || '/'
    router.push(redirect)
  }
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.login-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 3rem;
  width: 100%;
  max-width: 400px;
  position: relative;
  z-index: 2;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 1rem;
}

.logo i {
  font-size: 2rem;
  color: var(--accent);
}

.logo h1 {
  font-size: 1.5rem;
  color: var(--secondary);
  margin: 0;
}

.subtitle {
  color: #666;
  font-size: 1rem;
  margin: 0;
}

.login-form {
  margin-bottom: 1.5rem;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.login-btn {
  width: 100%;
  height: 48px;
  font-size: 1rem;
  font-weight: 600;
}

.divider {
  text-align: center;
  margin: 1.5rem 0;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--gray);
}

.divider span {
  background: white;
  padding: 0 1rem;
  color: #999;
  font-size: 0.9rem;
}

.quick-login {
  text-align: center;
  margin-bottom: 1.5rem;
}

.quick-login-title {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.demo-accounts {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.register-link {
  text-align: center;
  color: #666;
  font-size: 0.9rem;
}

.link {
  color: var(--primary);
  text-decoration: none;
  font-weight: 500;
  margin-left: 0.5rem;
}

.link:hover {
  text-decoration: underline;
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.decoration-item {
  position: absolute;
  color: rgba(255, 255, 255, 0.1);
  font-size: 3rem;
  animation: float 6s ease-in-out infinite;
}

.item-1 {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.item-2 {
  top: 20%;
  right: 15%;
  animation-delay: 1.5s;
}

.item-3 {
  bottom: 20%;
  left: 15%;
  animation-delay: 3s;
}

.item-4 {
  bottom: 10%;
  right: 10%;
  animation-delay: 4.5s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    padding: 1rem;
  }
  
  .login-card {
    padding: 2rem;
  }
  
  .demo-accounts {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>
