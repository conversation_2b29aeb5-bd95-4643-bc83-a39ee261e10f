package com.petadoption.util;

import lombok.Data;

import java.util.List;

/**
 * 分页工具类
 * 与前端分页组件保持一致的数据格式
 * 
 * <AUTHOR> Team
 */
public class PageUtil {
    
    /**
     * 默认页码
     */
    public static final int DEFAULT_PAGE = 1;
    
    /**
     * 默认页面大小
     */
    public static final int DEFAULT_SIZE = 10;
    
    /**
     * 最大页面大小
     */
    public static final int MAX_SIZE = 100;
    
    /**
     * 处理页码
     *
     * @param page 页码（从1开始）
     * @return 处理后的页码
     */
    public static int getPageNumber(Integer page) {
        return (page != null && page > 0) ? page : DEFAULT_PAGE;
    }

    /**
     * 处理页面大小
     *
     * @param size 页面大小
     * @return 处理后的页面大小
     */
    public static int getPageSize(Integer size) {
        return (size != null && size > 0) ? Math.min(size, MAX_SIZE) : DEFAULT_SIZE;
    }
    
    /**
     * 分页响应类
     * 与前端分页组件的数据格式保持一致
     */
    @Data
    public static class PageResponse<T> {
        /**
         * 数据列表
         */
        private List<T> data;
        
        /**
         * 总记录数
         */
        private Long total;
        
        /**
         * 当前页码（从1开始）
         */
        private Integer page;
        
        /**
         * 页面大小
         */
        private Integer size;
        
        /**
         * 总页数
         */
        private Integer totalPages;
        
        /**
         * 是否为第一页
         */
        private Boolean first;
        
        /**
         * 是否为最后一页
         */
        private Boolean last;
        
        /**
         * 是否有下一页
         */
        private Boolean hasNext;
        
        /**
         * 是否有上一页
         */
        private Boolean hasPrevious;
    }
}
