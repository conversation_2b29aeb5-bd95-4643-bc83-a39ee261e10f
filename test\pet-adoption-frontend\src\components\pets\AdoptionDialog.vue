<template>
  <el-dialog
    v-model="dialogVisible"
    title="申请领养"
    width="600px"
    :before-close="handleClose"
    class="adoption-dialog"
  >
    <div v-if="pet" class="dialog-content">
      <!-- 宠物信息 -->
      <div class="pet-summary">
        <div class="pet-avatar">
          <i :class="getPetIcon(pet.species)"></i>
        </div>
        <div class="pet-basic-info">
          <h3 class="pet-name">{{ pet.name }}</h3>
          <div class="pet-details">
            <span><i class="fas fa-tag"></i> {{ pet.breed }}</span>
            <span><i class="fas fa-birthday-cake"></i> {{ pet.age }}岁</span>
            <span><i class="fas fa-venus-mars"></i> {{ pet.gender }}</span>
          </div>
        </div>
      </div>

      <!-- 申请表单 -->
      <el-form
        ref="applicationFormRef"
        :model="applicationForm"
        :rules="applicationRules"
        label-width="120px"
        class="application-form"
      >
        <el-form-item label="申请理由" prop="reason">
          <el-select
            v-model="applicationForm.reason"
            placeholder="请选择申请理由"
            style="width: 100%"
          >
            <el-option label="喜欢这个品种" value="breed_preference" />
            <el-option label="为孩子寻找伙伴" value="companion_for_child" />
            <el-option label="个人陪伴需求" value="personal_companion" />
            <el-option label="有养宠经验" value="experienced_owner" />
            <el-option label="救助流浪动物" value="rescue_animal" />
            <el-option label="其他原因" value="other" />
          </el-select>
        </el-form-item>

        <el-form-item label="养宠经验" prop="experience">
          <el-radio-group v-model="applicationForm.experience">
            <el-radio label="none">没有经验</el-radio>
            <el-radio label="some">有一些经验</el-radio>
            <el-radio label="experienced">经验丰富</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="居住环境" prop="living_situation">
          <el-select
            v-model="applicationForm.living_situation"
            placeholder="请选择居住环境"
            style="width: 100%"
          >
            <el-option label="独立房屋带院子" value="house_with_yard" />
            <el-option label="独立房屋无院子" value="house_no_yard" />
            <el-option label="公寓/楼房" value="apartment" />
            <el-option label="宿舍" value="dormitory" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>

        <el-form-item label="家庭成员" prop="family_members">
          <el-input-number
            v-model="applicationForm.family_members"
            :min="1"
            :max="20"
            style="width: 100%"
          />
          <div class="form-tip">包括您自己在内的家庭成员数量</div>
        </el-form-item>

        <el-form-item label="是否有其他宠物" prop="has_other_pets">
          <el-radio-group v-model="applicationForm.has_other_pets">
            <el-radio :label="false">没有</el-radio>
            <el-radio :label="true">有</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item 
          v-if="applicationForm.has_other_pets" 
          label="其他宠物描述" 
          prop="other_pets_description"
        >
          <el-input
            v-model="applicationForm.other_pets_description"
            type="textarea"
            :rows="2"
            placeholder="请描述您现有的其他宠物情况"
          />
        </el-form-item>

        <el-form-item label="每日陪伴时间" prop="daily_time">
          <el-select
            v-model="applicationForm.daily_time"
            placeholder="请选择每日可陪伴时间"
            style="width: 100%"
          >
            <el-option label="1-2小时" value="1-2" />
            <el-option label="3-4小时" value="3-4" />
            <el-option label="5-6小时" value="5-6" />
            <el-option label="全天在家" value="full_time" />
          </el-select>
        </el-form-item>

        <el-form-item label="月预算" prop="monthly_budget">
          <el-select
            v-model="applicationForm.monthly_budget"
            placeholder="请选择月度预算"
            style="width: 100%"
          >
            <el-option label="200-500元" value="200-500" />
            <el-option label="500-1000元" value="500-1000" />
            <el-option label="1000-2000元" value="1000-2000" />
            <el-option label="2000元以上" value="2000+" />
          </el-select>
        </el-form-item>

        <el-form-item label="详细说明" prop="notes">
          <el-input
            v-model="applicationForm.notes"
            type="textarea"
            :rows="4"
            placeholder="请详细说明您的养宠条件、经验和对这只宠物的了解..."
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item prop="agreement">
          <el-checkbox v-model="applicationForm.agreement">
            我已阅读并同意
            <el-link type="primary" @click="showAdoptionAgreement">
              《宠物领养协议》
            </el-link>
            ，承诺会善待宠物并承担相应责任
          </el-checkbox>
        </el-form-item>
      </el-form>

      <!-- 温馨提示 -->
      <el-alert
        title="温馨提示"
        type="info"
        :closable="false"
        class="adoption-tips"
      >
        <template #default>
          <ul class="tips-list">
            <li>请如实填写申请信息，我们会认真审核每一份申请</li>
            <li>审核通过后，我们会在3个工作日内联系您</li>
            <li>领养前需要签署正式的领养协议</li>
            <li>我们会定期回访，确保宠物得到良好照顾</li>
          </ul>
        </template>
      </el-alert>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="submitting"
          @click="submitApplication"
        >
          <i class="fas fa-heart"></i>
          提交申请
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { applicationApi } from '@/api/applications'
import { getPetIcon } from '@/utils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  pet: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

// 表单引用
const applicationFormRef = ref()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 提交状态
const submitting = ref(false)

// 申请表单
const applicationForm = reactive({
  reason: '',
  experience: '',
  living_situation: '',
  family_members: 1,
  has_other_pets: false,
  other_pets_description: '',
  daily_time: '',
  monthly_budget: '',
  notes: '',
  agreement: false
})

// 表单验证规则
const applicationRules = {
  reason: [
    { required: true, message: '请选择申请理由', trigger: 'change' }
  ],
  experience: [
    { required: true, message: '请选择养宠经验', trigger: 'change' }
  ],
  living_situation: [
    { required: true, message: '请选择居住环境', trigger: 'change' }
  ],
  family_members: [
    { required: true, message: '请填写家庭成员数量', trigger: 'blur' }
  ],
  has_other_pets: [
    { required: true, message: '请选择是否有其他宠物', trigger: 'change' }
  ],
  other_pets_description: [
    { 
      validator: (rule, value, callback) => {
        if (applicationForm.has_other_pets && !value) {
          callback(new Error('请描述其他宠物情况'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ],
  daily_time: [
    { required: true, message: '请选择每日陪伴时间', trigger: 'change' }
  ],
  monthly_budget: [
    { required: true, message: '请选择月度预算', trigger: 'change' }
  ],
  notes: [
    { required: true, message: '请填写详细说明', trigger: 'blur' },
    { min: 20, message: '详细说明至少20个字符', trigger: 'blur' }
  ],
  agreement: [
    { 
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error('请阅读并同意领养协议'))
        } else {
          callback()
        }
      }, 
      trigger: 'change' 
    }
  ]
}

// 重置表单
const resetForm = () => {
  Object.assign(applicationForm, {
    reason: '',
    experience: '',
    living_situation: '',
    family_members: 1,
    has_other_pets: false,
    other_pets_description: '',
    daily_time: '',
    monthly_budget: '',
    notes: '',
    agreement: false
  })
  
  if (applicationFormRef.value) {
    applicationFormRef.value.clearValidate()
  }
}

// 提交申请
const submitApplication = async () => {
  if (!applicationFormRef.value) return
  
  try {
    await applicationFormRef.value.validate()
    
    submitting.value = true
    
    // 构建申请数据
    const applicationData = {
      petId: props.pet.id,
      reason: applicationForm.reason,
      experience: applicationForm.experience,
      livingSituation: applicationForm.living_situation,
      familyMembers: applicationForm.family_members,
      hasOtherPets: applicationForm.has_other_pets,
      otherPetsDescription: applicationForm.other_pets_description,
      dailyTime: applicationForm.daily_time,
      monthlyBudget: applicationForm.monthly_budget,
      notes: applicationForm.notes
    }
    
    await applicationApi.createApplication(applicationData)
    
    ElMessage.success('领养申请提交成功！我们会尽快审核您的申请')
    emit('success')
    handleClose()
    
  } catch (error) {
    console.error('提交申请失败:', error)

    // 根据错误类型显示不同的消息
    let errorMessage = '提交申请失败，请稍后重试'

    if (error.message) {
      // 如果有具体的错误消息，直接显示
      errorMessage = error.message
    } else if (error.response?.data?.message) {
      // 如果有响应中的错误消息
      errorMessage = error.response.data.message
    }

    ElMessage.error(errorMessage)
  } finally {
    submitting.value = false
  }
}

// 显示领养协议
const showAdoptionAgreement = () => {
  ElMessageBox.alert(
    `
    <div style="text-align: left; line-height: 1.6;">
      <h3>宠物领养协议</h3>
      <p><strong>1. 领养责任</strong></p>
      <p>领养人承诺为宠物提供充足的食物、水、住所和医疗照顾。</p>
      
      <p><strong>2. 不得遗弃</strong></p>
      <p>领养人承诺不会遗弃、虐待或转让宠物。</p>
      
      <p><strong>3. 医疗保健</strong></p>
      <p>领养人应定期为宠物进行健康检查和必要的疫苗接种。</p>
      
      <p><strong>4. 回访配合</strong></p>
      <p>领养人同意配合我们的定期回访，提供宠物近况。</p>
      
      <p><strong>5. 紧急联系</strong></p>
      <p>如遇特殊情况无法继续照顾宠物，应及时联系我们协助处理。</p>
    </div>
    `,
    '宠物领养协议',
    {
      confirmButtonText: '我已阅读',
      dangerouslyUseHTMLString: true,
      customStyle: {
        width: '500px'
      }
    }
  )
}

// 关闭对话框
const handleClose = () => {
  resetForm()
  dialogVisible.value = false
}

// 监听对话框打开
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      resetForm()
    }
  }
)
</script>

<style scoped>
.adoption-dialog {
  --el-dialog-border-radius: 12px;
}

.dialog-content {
  padding: 0 1rem;
}

.pet-summary {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--light);
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.pet-avatar {
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #64b3f4, #c2e59c);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.pet-basic-info {
  flex: 1;
}

.pet-name {
  font-size: 1.2rem;
  color: var(--secondary);
  margin-bottom: 0.5rem;
}

.pet-details {
  display: flex;
  gap: 1rem;
  color: #666;
  font-size: 0.9rem;
}

.pet-details span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.application-form {
  margin-bottom: 1.5rem;
}

.form-tip {
  font-size: 0.8rem;
  color: #999;
  margin-top: 0.3rem;
}

.adoption-tips {
  margin-bottom: 1rem;
}

.tips-list {
  margin: 0;
  padding-left: 1.2rem;
}

.tips-list li {
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pet-summary {
    flex-direction: column;
    text-align: center;
  }
  
  .pet-details {
    justify-content: center;
    flex-wrap: wrap;
  }
}
</style>
