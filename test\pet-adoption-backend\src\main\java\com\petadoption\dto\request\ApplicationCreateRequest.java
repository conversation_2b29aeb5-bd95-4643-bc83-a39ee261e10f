package com.petadoption.dto.request;

import lombok.Data;

import jakarta.validation.constraints.*;

/**
 * 申请创建请求DTO
 * 与前端申请表单数据结构保持一致
 * 
 * <AUTHOR> Team
 */
@Data
public class ApplicationCreateRequest {
    
    /**
     * 申请宠物ID
     */
    @NotNull(message = "申请宠物ID不能为空")
    private Long petId;
    
    /**
     * 申请理由
     */
    @NotBlank(message = "申请理由不能为空")
    private String reason;
    
    /**
     * 养宠经验
     */
    @NotBlank(message = "养宠经验不能为空")
    private String experience;
    
    /**
     * 居住环境
     */
    @NotBlank(message = "居住环境不能为空")
    private String livingSituation;
    
    /**
     * 家庭成员数量
     */
    @NotNull(message = "家庭成员数量不能为空")
    @Min(value = 1, message = "家庭成员数量不能小于1")
    @Max(value = 20, message = "家庭成员数量不能大于20")
    private Integer familyMembers;
    
    /**
     * 是否有其他宠物
     */
    @NotNull(message = "是否有其他宠物不能为空")
    private Boolean hasOtherPets;
    
    /**
     * 其他宠物描述
     */
    @Size(max = 200, message = "其他宠物描述长度不能超过200个字符")
    private String otherPetsDescription;
    
    /**
     * 每日陪伴时间
     */
    @NotBlank(message = "每日陪伴时间不能为空")
    private String dailyTime;
    
    /**
     * 月度预算
     */
    @NotBlank(message = "月度预算不能为空")
    private String monthlyBudget;
    
    /**
     * 详细说明
     */
    @NotBlank(message = "详细说明不能为空")
    @Size(min = 20, max = 500, message = "详细说明长度必须在20-500个字符之间")
    private String notes;
    
    /**
     * 申请人联系电话
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "联系电话格式不正确")
    private String contactPhone;
    
    /**
     * 申请人联系邮箱
     */
    @Email(message = "联系邮箱格式不正确")
    private String contactEmail;
    
    /**
     * 紧急联系人姓名
     */
    @Size(max = 20, message = "紧急联系人姓名长度不能超过20个字符")
    private String emergencyContactName;
    
    /**
     * 紧急联系人电话
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "紧急联系人电话格式不正确")
    private String emergencyContactPhone;
    
    /**
     * 是否同意家访
     */
    private Boolean agreeHomeVisit = true;
    
    /**
     * 是否同意定期回访
     */
    private Boolean agreeFollowUp = true;
}
