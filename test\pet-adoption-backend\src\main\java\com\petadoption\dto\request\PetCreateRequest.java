package com.petadoption.dto.request;

import lombok.Data;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 宠物创建请求DTO
 * 与前端宠物创建表单数据结构保持一致
 * 
 * <AUTHOR> Team
 */
@Data
public class PetCreateRequest {
    
    /**
     * 宠物名称
     */
    @NotBlank(message = "宠物名称不能为空")
    @Size(min = 1, max = 20, message = "宠物名称长度必须在1-20个字符之间")
    private String name;
    
    /**
     * 宠物种类
     */
    @NotBlank(message = "宠物种类不能为空")
    private String species;
    
    /**
     * 宠物品种
     */
    @NotBlank(message = "宠物品种不能为空")
    @Size(max = 30, message = "宠物品种长度不能超过30个字符")
    private String breed;
    
    /**
     * 年龄（岁）
     */
    @NotNull(message = "宠物年龄不能为空")
    @Min(value = 0, message = "宠物年龄不能小于0")
    @Max(value = 30, message = "宠物年龄不能大于30")
    private Integer age;
    
    /**
     * 性别
     */
    @NotBlank(message = "宠物性别不能为空")
    private String gender;
    
    /**
     * 体重（公斤）
     */
    @DecimalMin(value = "0.1", message = "宠物体重不能小于0.1公斤")
    @DecimalMax(value = "100.0", message = "宠物体重不能大于100公斤")
    private BigDecimal weight;
    
    /**
     * 颜色
     */
    @Size(max = 20, message = "宠物颜色描述长度不能超过20个字符")
    private String color;
    
    /**
     * 健康状况
     */
    @NotBlank(message = "健康状况不能为空")
    @Size(max = 50, message = "健康状况描述长度不能超过50个字符")
    private String healthStatus;
    
    /**
     * 疫苗接种情况
     */
    @Size(max = 100, message = "疫苗接种情况描述长度不能超过100个字符")
    private String vaccinationStatus;
    
    /**
     * 是否绝育
     */
    private Boolean isNeutered = false;
    
    /**
     * 详细描述
     */
    @NotBlank(message = "宠物描述不能为空")
    @Size(min = 10, max = 1000, message = "宠物描述长度必须在10-1000个字符之间")
    private String description;
    
    /**
     * 宠物图片URL
     */
    private String imageUrl;
    
    /**
     * 救助地点
     */
    @Size(max = 100, message = "救助地点描述长度不能超过100个字符")
    private String rescueLocation;
    
    /**
     * 救助时间
     */
    private LocalDateTime rescueTime;
    
    /**
     * 特殊需求
     */
    @Size(max = 200, message = "特殊需求描述长度不能超过200个字符")
    private String specialNeeds;
    
    /**
     * 性格特点
     */
    @Size(max = 200, message = "性格特点描述长度不能超过200个字符")
    private String personality;
    
    /**
     * 是否适合有孩子的家庭
     */
    private Boolean goodWithKids;
    
    /**
     * 是否适合与其他宠物相处
     */
    private Boolean goodWithPets;
    
    /**
     * 活动需求等级（1-5）
     */
    @Min(value = 1, message = "活动需求等级不能小于1")
    @Max(value = 5, message = "活动需求等级不能大于5")
    private Integer activityLevel;
    
    /**
     * 训练程度（1-5）
     */
    @Min(value = 1, message = "训练程度不能小于1")
    @Max(value = 5, message = "训练程度不能大于5")
    private Integer trainingLevel;
}
