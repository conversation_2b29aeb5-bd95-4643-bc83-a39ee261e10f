<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" th:replace="~{layout/base :: html}">
<head>
    <title th:text="${pet.name} + ' - 宠物详情'">宠物详情 - 宠物领养系统</title>
</head>
<body>
    <th:block th:fragment="content">
        <div class="container py-4">
            <!-- 返回按钮 -->
            <div class="row mb-3">
                <div class="col">
                    <a href="/web/pets" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回列表
                    </a>
                </div>
            </div>

            <!-- 宠物详情 -->
            <div class="row" th:if="${pet != null}">
                <!-- 宠物图片 -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="position-relative">
                            <img th:src="${pet.imageUrl} ?: '/images/default-pet.jpg'" 
                                 class="card-img-top" 
                                 th:alt="${pet.name}"
                                 style="height: 400px; object-fit: cover;">
                            <span class="badge bg-success position-absolute top-0 end-0 m-3" 
                                  th:if="${!pet.isAdopted}">
                                可领养
                            </span>
                            <span class="badge bg-secondary position-absolute top-0 end-0 m-3" 
                                  th:if="${pet.isAdopted}">
                                已领养
                            </span>
                        </div>
                    </div>
                </div>

                <!-- 宠物信息 -->
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-body">
                            <h1 class="card-title fw-bold mb-3" th:text="${pet.name}">宠物名称</h1>
                            
                            <!-- 基本信息 -->
                            <div class="row mb-3">
                                <div class="col-6">
                                    <strong>种类：</strong>
                                    <span th:text="${pet.species}">种类</span>
                                </div>
                                <div class="col-6">
                                    <strong>品种：</strong>
                                    <span th:text="${pet.breed}">品种</span>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-6">
                                    <strong>年龄：</strong>
                                    <span th:text="${pet.age} + '岁'">年龄</span>
                                </div>
                                <div class="col-6">
                                    <strong>性别：</strong>
                                    <span th:text="${pet.gender}">性别</span>
                                </div>
                            </div>
                            
                            <div class="row mb-3" th:if="${pet.weight != null}">
                                <div class="col-6">
                                    <strong>体重：</strong>
                                    <span th:text="${pet.weight} + 'kg'">体重</span>
                                </div>
                                <div class="col-6" th:if="${pet.color != null}">
                                    <strong>颜色：</strong>
                                    <span th:text="${pet.color}">颜色</span>
                                </div>
                            </div>
                            
                            <!-- 健康信息 -->
                            <div class="mb-3">
                                <strong>健康状况：</strong>
                                <span th:text="${pet.healthStatus}">健康状况</span>
                            </div>
                            
                            <div class="mb-3" th:if="${pet.vaccinationStatus != null}">
                                <strong>疫苗接种：</strong>
                                <span th:text="${pet.vaccinationStatus}">疫苗接种情况</span>
                            </div>
                            
                            <div class="mb-3">
                                <strong>是否绝育：</strong>
                                <span th:text="${pet.isNeutered ? '是' : '否'}">绝育状态</span>
                            </div>
                            
                            <!-- 性格特点 -->
                            <div class="mb-3" th:if="${pet.personality != null}">
                                <strong>性格特点：</strong>
                                <p th:text="${pet.personality}" class="mb-0">性格特点</p>
                            </div>
                            
                            <!-- 适应性 -->
                            <div class="row mb-3" th:if="${pet.goodWithKids != null or pet.goodWithPets != null}">
                                <div class="col-12">
                                    <strong>适应性：</strong>
                                </div>
                                <div class="col-12">
                                    <span class="badge bg-info me-2" th:if="${pet.goodWithKids}">
                                        <i class="fas fa-child me-1"></i>适合有孩子的家庭
                                    </span>
                                    <span class="badge bg-info me-2" th:if="${pet.goodWithPets}">
                                        <i class="fas fa-paw me-1"></i>适合与其他宠物相处
                                    </span>
                                </div>
                            </div>
                            
                            <!-- 活动需求 -->
                            <div class="row mb-3" th:if="${pet.activityLevel != null or pet.trainingLevel != null}">
                                <div class="col-6" th:if="${pet.activityLevel != null}">
                                    <strong>活动需求：</strong>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar" 
                                             th:style="'width: ' + (${pet.activityLevel} * 20) + '%'"
                                             th:text="${pet.activityLevel} + '/5'">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6" th:if="${pet.trainingLevel != null}">
                                    <strong>训练程度：</strong>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-success" 
                                             th:style="'width: ' + (${pet.trainingLevel} * 20) + '%'"
                                             th:text="${pet.trainingLevel} + '/5'">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 操作按钮 -->
                            <div class="mt-4">
                                <a th:href="@{/web/applications/create(petId=${pet.id})}" 
                                   class="btn btn-success btn-lg me-2"
                                   th:if="${!pet.isAdopted}">
                                    <i class="fas fa-heart me-2"></i>申请领养
                                </a>
                                <button class="btn btn-secondary btn-lg" 
                                        th:if="${pet.isAdopted}" disabled>
                                    <i class="fas fa-check me-2"></i>已被领养
                                </button>
                                <button class="btn btn-outline-primary" onclick="sharePet()">
                                    <i class="fas fa-share me-1"></i>分享
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 详细描述 -->
            <div class="row mt-4" th:if="${pet != null}">
                <div class="col">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>详细介绍
                            </h5>
                        </div>
                        <div class="card-body">
                            <p th:text="${pet.description}" class="mb-0">宠物详细描述</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 救助信息 -->
            <div class="row mt-4" th:if="${pet != null and (pet.rescueLocation != null or pet.rescueTime != null)}">
                <div class="col">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-map-marker-alt me-2"></i>救助信息
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6" th:if="${pet.rescueLocation != null}">
                                    <strong>救助地点：</strong>
                                    <span th:text="${pet.rescueLocation}">救助地点</span>
                                </div>
                                <div class="col-md-6" th:if="${pet.rescueTime != null}">
                                    <strong>救助时间：</strong>
                                    <span th:text="${#temporals.format(pet.rescueTime, 'yyyy-MM-dd')}">救助时间</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 推荐宠物 -->
            <div class="row mt-5" th:if="${recommendedPets != null and !recommendedPets.isEmpty()}">
                <div class="col">
                    <h4 class="mb-4">
                        <i class="fas fa-heart me-2 text-danger"></i>您可能还喜欢
                    </h4>
                    <div class="row">
                        <div class="col-md-4 mb-3" th:each="recommendedPet : ${recommendedPets}">
                            <div class="card pet-card h-100">
                                <div class="position-relative">
                                    <img th:src="${recommendedPet.imageUrl} ?: '/images/default-pet.jpg'" 
                                         class="card-img-top" 
                                         th:alt="${recommendedPet.name}"
                                         style="height: 200px; object-fit: cover;">
                                    <span class="badge bg-success status-badge" th:if="${!recommendedPet.isAdopted}">
                                        可领养
                                    </span>
                                </div>
                                <div class="card-body">
                                    <h6 class="card-title" th:text="${recommendedPet.name}">宠物名称</h6>
                                    <p class="text-muted small mb-2">
                                        <span th:text="${recommendedPet.species}">种类</span> · 
                                        <span th:text="${recommendedPet.age} + '岁'">年龄</span>
                                    </p>
                                    <a th:href="@{/web/pets/{id}(id=${recommendedPet.id})}" 
                                       class="btn btn-outline-primary btn-sm">
                                        查看详情
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </th:block>

    <th:block th:fragment="scripts">
        <script>
            function sharePet() {
                if (navigator.share) {
                    navigator.share({
                        title: '[[${pet.name}]] - 宠物领养',
                        text: '快来看看这只可爱的[[${pet.species}]]！',
                        url: window.location.href
                    });
                } else {
                    // 复制链接到剪贴板
                    navigator.clipboard.writeText(window.location.href).then(function() {
                        alert('链接已复制到剪贴板！');
                    });
                }
            }
        </script>
    </th:block>
</body>
</html>
