<template>
  <div class="pet-detail-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 宠物详情 -->
    <div v-else-if="pet" class="pet-detail-content">
      <!-- 返回按钮 -->
      <div class="back-button">
        <el-button @click="goBack" type="info" plain>
          <i class="fas fa-arrow-left"></i>
          返回列表
        </el-button>
      </div>

      <!-- 宠物主要信息 -->
      <el-card class="pet-main-card">
        <div class="pet-main-content">
          <!-- 宠物图片 -->
          <div class="pet-image-section">
            <div class="pet-main-image">
              <i :class="getPetIcon(pet.species)"></i>
            </div>
            <div class="pet-status">
              <el-tag
                :type="pet.is_adopted ? 'danger' : 'success'"
                size="large"
                effect="dark"
              >
                <i :class="pet.is_adopted ? 'fas fa-home' : 'fas fa-heart'"></i>
                {{ getStatusText(pet.is_adopted, 'pet') }}
              </el-tag>
            </div>
          </div>

          <!-- 宠物信息 -->
          <div class="pet-info-section">
            <div class="pet-header">
              <h1 class="pet-name">{{ pet.name }}</h1>
              <div class="pet-id">ID: {{ pet.id }}</div>
            </div>

            <div class="pet-basic-info">
              <div class="info-grid">
                <div class="info-item">
                  <div class="info-label">
                    <i class="fas fa-tag"></i>
                    品种
                  </div>
                  <div class="info-value">{{ pet.breed }}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">
                    <i class="fas fa-paw"></i>
                    种类
                  </div>
                  <div class="info-value">{{ pet.species }}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">
                    <i class="fas fa-birthday-cake"></i>
                    年龄
                  </div>
                  <div class="info-value">{{ pet.age }}岁</div>
                </div>
                <div class="info-item">
                  <div class="info-label">
                    <i class="fas fa-venus-mars"></i>
                    性别
                  </div>
                  <div class="info-value">{{ pet.gender }}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">
                    <i class="fas fa-heartbeat"></i>
                    健康状况
                  </div>
                  <div class="info-value">{{ pet.health_status }}</div>
                </div>
                <div class="info-item">
                  <div class="info-label">
                    <i class="fas fa-calendar-alt"></i>
                    入站时间
                  </div>
                  <div class="info-value">{{ formatDate(pet.create_time) }}</div>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="pet-actions">
              <el-button
                v-if="!pet.is_adopted && authStore.isAuthenticated"
                type="primary"
                size="large"
                @click="showAdoptionDialog"
              >
                <i class="fas fa-heart"></i>
                申请领养
              </el-button>
              <el-button
                v-else-if="!authStore.isAuthenticated"
                type="primary"
                size="large"
                @click="goToLogin"
              >
                <i class="fas fa-sign-in-alt"></i>
                登录后申请领养
              </el-button>
              <el-button
                type="info"
                size="large"
                @click="sharePet"
              >
                <i class="fas fa-share-alt"></i>
                分享
              </el-button>
              <el-button
                v-if="authStore.isAdmin"
                type="warning"
                size="large"
                @click="editPet"
              >
                <i class="fas fa-edit"></i>
                编辑
              </el-button>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 详细描述 -->
      <el-card class="pet-description-card">
        <template #header>
          <div class="card-header">
            <h2>
              <i class="fas fa-info-circle"></i>
              详细介绍
            </h2>
          </div>
        </template>
        <div class="pet-description">
          <p>{{ pet.description }}</p>
        </div>
      </el-card>

      <!-- 领养须知 -->
      <el-card class="adoption-requirements-card">
        <template #header>
          <div class="card-header">
            <h2>
              <i class="fas fa-clipboard-list"></i>
              领养须知
            </h2>
          </div>
        </template>
        <div class="requirements-content">
          <div class="requirement-item">
            <div class="requirement-icon">
              <i class="fas fa-home"></i>
            </div>
            <div class="requirement-text">
              <h3>稳定的居住环境</h3>
              <p>需要有固定的住所，能够为宠物提供安全舒适的生活空间</p>
            </div>
          </div>
          <div class="requirement-item">
            <div class="requirement-icon">
              <i class="fas fa-clock"></i>
            </div>
            <div class="requirement-text">
              <h3>充足的陪伴时间</h3>
              <p>每天至少有2-3小时陪伴宠物，给予足够的关爱和互动</p>
            </div>
          </div>
          <div class="requirement-item">
            <div class="requirement-icon">
              <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="requirement-text">
              <h3>经济能力保障</h3>
              <p>有稳定的经济来源，能够承担宠物的日常开销和医疗费用</p>
            </div>
          </div>
          <div class="requirement-item">
            <div class="requirement-icon">
              <i class="fas fa-heart"></i>
            </div>
            <div class="requirement-text">
              <h3>长期承诺</h3>
              <p>承诺会照顾宠物一生，不会因为任何原因遗弃或转让</p>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 相关宠物推荐 -->
      <el-card v-if="relatedPets.length > 0" class="related-pets-card">
        <template #header>
          <div class="card-header">
            <h2>
              <i class="fas fa-star"></i>
              相关推荐
            </h2>
          </div>
        </template>
        <div class="related-pets-grid">
          <div
            v-for="relatedPet in relatedPets"
            :key="relatedPet.id"
            class="related-pet-card"
            @click="goToPetDetail(relatedPet.id)"
          >
            <div class="related-pet-image">
              <i :class="getPetIcon(relatedPet.species)"></i>
            </div>
            <div class="related-pet-info">
              <h4 class="related-pet-name">{{ relatedPet.name }}</h4>
              <p class="related-pet-breed">{{ relatedPet.breed }}</p>
              <span :class="getStatusClass(relatedPet.is_adopted, 'pet')">
                {{ getStatusText(relatedPet.is_adopted, 'pet') }}
              </span>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 宠物不存在 -->
    <div v-else class="not-found">
      <el-result
        icon="warning"
        title="宠物不存在"
        sub-title="抱歉，您访问的宠物信息不存在或已被删除"
      >
        <template #extra>
          <el-button type="primary" @click="goBack">
            <i class="fas fa-arrow-left"></i>
            返回列表
          </el-button>
        </template>
      </el-result>
    </div>

    <!-- 领养申请对话框 -->
    <AdoptionDialog
      v-model="showDialog"
      :pet="pet"
      @success="handleAdoptionSuccess"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { usePetsStore } from '@/stores/pets'
import { getPetIcon, getStatusText, getStatusClass, formatDate } from '@/utils'
import AdoptionDialog from '@/components/pets/AdoptionDialog.vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const petsStore = usePetsStore()

// 状态
const loading = ref(true)
const showDialog = ref(false)

// 宠物ID
const petId = computed(() => parseInt(route.params.id))

// 当前宠物
const pet = ref(null)

// 相关宠物推荐
const relatedPets = computed(() => {
  if (!pet.value) return []
  
  return petsStore.pets
    .filter(p => 
      p.id !== pet.value.id && 
      p.species === pet.value.species && 
      !p.is_adopted
    )
    .slice(0, 4)
})

// 获取宠物详情
const fetchPetDetail = async () => {
  try {
    loading.value = true
    const result = await petsStore.fetchPetDetail(petId.value)
    
    if (result.success) {
      pet.value = result.data
    } else {
      pet.value = null
      ElMessage.error(result.message)
    }
  } catch (error) {
    console.error('获取宠物详情失败:', error)
    pet.value = null
    ElMessage.error('获取宠物详情失败')
  } finally {
    loading.value = false
  }
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 跳转到登录页
const goToLogin = () => {
  router.push(`/login?redirect=${route.fullPath}`)
}

// 显示领养对话框
const showAdoptionDialog = () => {
  showDialog.value = true
}

// 领养申请成功
const handleAdoptionSuccess = () => {
  ElMessage.success('领养申请提交成功！')
  showDialog.value = false
  // 刷新宠物信息
  fetchPetDetail()
}

// 分享宠物
const sharePet = () => {
  const url = window.location.href
  const title = `${pet.value.name} - 宠物领养`
  const text = `${pet.value.name}是一只可爱的${pet.value.breed}，正在寻找温暖的家。`
  
  if (navigator.share) {
    navigator.share({
      title,
      text,
      url
    }).catch(console.error)
  } else {
    // 复制链接到剪贴板
    navigator.clipboard.writeText(url).then(() => {
      ElMessage.success('链接已复制到剪贴板')
    }).catch(() => {
      ElMessage.info(`请复制链接分享：${url}`)
    })
  }
}

// 编辑宠物
const editPet = () => {
  router.push(`/manage/edit/${pet.value.id}`)
}

// 跳转到其他宠物详情
const goToPetDetail = (id) => {
  router.push(`/pets/${id}`)
}

// 监听路由变化
watch(
  () => route.params.id,
  (newId) => {
    if (newId) {
      fetchPetDetail()
    }
  }
)

onMounted(() => {
  fetchPetDetail()
  // 确保有宠物列表数据用于推荐
  if (petsStore.pets.length === 0) {
    petsStore.fetchPets()
  }
})
</script>

<style scoped>
.pet-detail-container {
  max-width: 1000px;
  margin: 0 auto;
}

.loading-container {
  padding: 2rem;
}

.back-button {
  margin-bottom: 1.5rem;
}

.pet-main-card {
  margin-bottom: 2rem;
  border-radius: 12px;
  box-shadow: var(--card-shadow);
}

.pet-main-content {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
  align-items: start;
}

.pet-image-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.pet-main-image {
  width: 250px;
  height: 250px;
  background: linear-gradient(45deg, #64b3f4, #c2e59c);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 6rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.pet-info-section {
  flex: 1;
}

.pet-header {
  margin-bottom: 1.5rem;
}

.pet-name {
  font-size: 2.5rem;
  color: var(--secondary);
  margin-bottom: 0.5rem;
}

.pet-id {
  color: #999;
  font-size: 0.9rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.info-item {
  padding: 1rem;
  background: var(--light);
  border-radius: 8px;
  transition: var(--transition);
}

.info-item:hover {
  background: #e8f4fd;
}

.info-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: var(--secondary);
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.info-label i {
  color: var(--accent);
}

.info-value {
  font-size: 1.1rem;
  color: var(--dark);
}

.pet-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card-header h2 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--secondary);
  margin: 0;
}

.card-header i {
  color: var(--accent);
}

.pet-description-card,
.adoption-requirements-card,
.related-pets-card {
  margin-bottom: 2rem;
  border-radius: 12px;
  box-shadow: var(--card-shadow);
}

.pet-description {
  font-size: 1.1rem;
  line-height: 1.8;
  color: var(--dark);
}

.requirements-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.requirement-item {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--light);
  border-radius: 8px;
  transition: var(--transition);
}

.requirement-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.requirement-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.requirement-text h3 {
  color: var(--secondary);
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.requirement-text p {
  color: #666;
  line-height: 1.5;
  font-size: 0.9rem;
}

.related-pets-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

.related-pet-card {
  background: var(--light);
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: var(--transition);
}

.related-pet-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.related-pet-image {
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #64b3f4, #c2e59c);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  margin: 0 auto 0.8rem;
}

.related-pet-name {
  font-size: 1rem;
  color: var(--secondary);
  margin-bottom: 0.3rem;
}

.related-pet-breed {
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pet-main-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .pet-main-image {
    width: 200px;
    height: 200px;
    font-size: 4rem;
  }
  
  .pet-name {
    font-size: 2rem;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .requirements-content {
    grid-template-columns: 1fr;
  }
  
  .related-pets-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .pet-actions {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .related-pets-grid {
    grid-template-columns: 1fr;
  }
  
  .pet-actions {
    flex-direction: column;
  }
  
  .pet-actions .el-button {
    width: 100%;
  }
}
</style>
