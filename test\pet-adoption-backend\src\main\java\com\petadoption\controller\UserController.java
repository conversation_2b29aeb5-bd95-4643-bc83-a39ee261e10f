package com.petadoption.controller;

import com.petadoption.common.Result;
import com.petadoption.dto.request.ChangePasswordRequest;
import com.petadoption.dto.request.UpdateProfileRequest;
import com.petadoption.dto.response.UserResponse;
import com.petadoption.exception.BusinessException;
import com.petadoption.service.AuthService;
import com.petadoption.service.UserService;
import com.petadoption.util.PageUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 用户控制器
 * 处理用户相关的HTTP请求
 * 与前端用户API保持一致
 * 
 * <AUTHOR> Team
 */
@Slf4j
@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
public class UserController {
    
    private final UserService userService;
    private final AuthService authService;
    
    /**
     * 获取当前用户资料
     * 
     * @return 用户响应
     */
    @GetMapping("/profile")
    public Result<UserResponse> getProfile() {
        Long currentUserId = authService.getCurrentUserId();
        UserResponse response = userService.getUserById(currentUserId);
        
        return Result.success("获取用户资料成功", response);
    }
    
    /**
     * 更新当前用户资料
     * 
     * @param request 更新请求
     * @return 用户响应
     */
    @PutMapping("/profile")
    public Result<UserResponse> updateProfile(@Valid @RequestBody UpdateProfileRequest request) {
        Long currentUserId = authService.getCurrentUserId();
        UserResponse response = userService.updateProfile(currentUserId, request);
        
        return Result.success("用户资料更新成功", response);
    }
    
    /**
     * 修改密码
     * 
     * @param request 修改密码请求
     * @return 响应结果
     */
    @PutMapping("/change-password")
    public Result<Void> changePassword(@Valid @RequestBody ChangePasswordRequest request) {
        // 验证确认密码
        if (!request.isPasswordMatch()) {
            throw new BusinessException("两次输入的密码不一致");
        }
        
        Long currentUserId = authService.getCurrentUserId();
        userService.changePassword(currentUserId, request);
        
        return Result.<Void>success();
    }
    
    /**
     * 获取用户详情（管理员）
     * 
     * @param userId 用户ID
     * @return 用户响应
     */
    @GetMapping("/{userId}")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<UserResponse> getUserById(@PathVariable Long userId) {
        UserResponse response = userService.getUserById(userId);
        
        return Result.success("获取用户详情成功", response);
    }
    
    /**
     * 更新指定用户资料（管理员）
     * 
     * @param userId 用户ID
     * @param request 更新请求
     * @return 用户响应
     */
    @PutMapping("/{userId}")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<UserResponse> updateUser(
            @PathVariable Long userId,
            @Valid @RequestBody UpdateProfileRequest request
    ) {
        UserResponse response = userService.updateProfile(userId, request);
        
        return Result.success("用户资料更新成功", response);
    }
    
    /**
     * 分页查询用户列表（管理员）
     * 
     * @param page 页码
     * @param size 页面大小
     * @param role 角色筛选
     * @param enabled 启用状态筛选
     * @param keyword 关键词搜索
     * @return 分页响应
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public Result<PageUtil.PageResponse<UserResponse>> getUsers(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String role,
            @RequestParam(required = false) Boolean enabled,
            @RequestParam(required = false) String keyword
    ) {
        PageUtil.PageResponse<UserResponse> response = userService.getUsers(
                page, size, role, enabled, keyword);
        
        return Result.success("获取用户列表成功", response);
    }
    
    /**
     * 更新用户状态（管理员）
     * 
     * @param userId 用户ID
     * @param enabled 启用状态
     * @return 响应结果
     */
    @PatchMapping("/{userId}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> updateUserStatus(
            @PathVariable Long userId,
            @RequestParam Boolean enabled
    ) {
        userService.updateUserStatus(userId, enabled);
        
        return Result.<Void>success();
    }
    
    /**
     * 锁定/解锁用户（管理员）
     * 
     * @param userId 用户ID
     * @param locked 锁定状态
     * @return 响应结果
     */
    @PatchMapping("/{userId}/lock")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> updateUserLockStatus(
            @PathVariable Long userId,
            @RequestParam Boolean locked
    ) {
        userService.updateUserLockStatus(userId, locked);
        
        return Result.<Void>success();
    }
    
    /**
     * 删除用户（管理员）
     * 
     * @param userId 用户ID
     * @return 响应结果
     */
    @DeleteMapping("/{userId}")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> deleteUser(@PathVariable Long userId) {
        userService.deleteUser(userId);
        
        return Result.<Void>success();
    }
}
