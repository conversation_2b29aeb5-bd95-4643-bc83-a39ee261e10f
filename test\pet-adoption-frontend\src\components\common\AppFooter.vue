<template>
  <footer class="app-footer">
    <div class="footer-container">
      <div class="footer-content">
        <!-- 关于我们 -->
        <div class="footer-section">
          <h3 class="footer-title">关于我们</h3>
          <p class="footer-description">
            我们致力于为流浪宠物寻找温暖的家，提供安全可靠的领养平台，
            让每个生命都能得到尊重和关爱。
          </p>
          <div class="social-links">
            <a href="#" class="social-link">
              <i class="fab fa-weixin"></i>
            </a>
            <a href="#" class="social-link">
              <i class="fab fa-weibo"></i>
            </a>
            <a href="#" class="social-link">
              <i class="fab fa-qq"></i>
            </a>
          </div>
        </div>

        <!-- 快速链接 -->
        <div class="footer-section">
          <h3 class="footer-title">快速链接</h3>
          <ul class="footer-links">
            <li>
              <router-link to="/">首页</router-link>
            </li>
            <li>
              <router-link to="/pets">宠物列表</router-link>
            </li>
            <li>
              <router-link to="/applications">领养申请</router-link>
            </li>
            <li>
              <a href="#" @click.prevent="scrollToTop">返回顶部</a>
            </li>
          </ul>
        </div>

        <!-- 联系我们 -->
        <div class="footer-section">
          <h3 class="footer-title">联系我们</h3>
          <ul class="contact-info">
            <li>
              <i class="fas fa-map-marker-alt"></i>
              <span>北京市朝阳区宠物救助中心</span>
            </li>
            <li>
              <i class="fas fa-phone"></i>
              <span>************</span>
            </li>
            <li>
              <i class="fas fa-envelope"></i>
              <span><EMAIL></span>
            </li>
            <li>
              <i class="fas fa-clock"></i>
              <span>服务时间：9:00-18:00</span>
            </li>
          </ul>
        </div>

        <!-- 统计信息 -->
        <div class="footer-section">
          <h3 class="footer-title">救助统计</h3>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-number">{{ stats.totalPets }}</div>
              <div class="stat-label">救助宠物</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ stats.adoptedPets }}</div>
              <div class="stat-label">成功领养</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ stats.totalUsers }}</div>
              <div class="stat-label">注册用户</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ stats.pendingApplications }}</div>
              <div class="stat-label">待处理申请</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 版权信息 -->
      <div class="footer-bottom">
        <div class="copyright">
          <p>&copy; {{ currentYear }} 宠物领养中心 - 让爱不再流浪</p>
          <p class="tech-info">
            基于 Vue3 + SpringBoot 构建 | 
            <a href="#" @click.prevent>隐私政策</a> | 
            <a href="#" @click.prevent>服务条款</a>
          </p>
        </div>
        <div class="footer-logo">
          <i class="fas fa-paw"></i>
          <span>宠物领养中心</span>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { scrollToTop } from '@/utils'

// 统计数据
const stats = ref({
  totalPets: 0,
  adoptedPets: 0,
  totalUsers: 0,
  pendingApplications: 0
})

// 当前年份
const currentYear = computed(() => new Date().getFullYear())

// 获取统计数据
const fetchStats = async () => {
  try {
    // 这里可以调用API获取真实统计数据
    // const response = await statsApi.getStats()
    // stats.value = response
    
    // 模拟数据
    stats.value = {
      totalPets: 156,
      adoptedPets: 89,
      totalUsers: 234,
      pendingApplications: 12
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

onMounted(() => {
  fetchStats()
})
</script>

<style scoped>
.app-footer {
  background: var(--dark);
  color: white;
  margin-top: auto;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 3rem 2rem 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section {
  display: flex;
  flex-direction: column;
}

.footer-title {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: var(--accent);
  font-weight: 600;
}

.footer-description {
  line-height: 1.6;
  color: #ccc;
  margin-bottom: 1rem;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: white;
  text-decoration: none;
  transition: var(--transition);
}

.social-link:hover {
  background: var(--accent);
  transform: translateY(-2px);
}

.footer-links {
  list-style: none;
  padding: 0;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-links a {
  color: #ccc;
  text-decoration: none;
  transition: var(--transition);
}

.footer-links a:hover {
  color: white;
}

.contact-info {
  list-style: none;
  padding: 0;
}

.contact-info li {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 0.8rem;
  color: #ccc;
}

.contact-info i {
  color: var(--accent);
  width: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  transition: var(--transition);
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.stat-number {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--accent);
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: #ccc;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid #444;
  flex-wrap: wrap;
  gap: 1rem;
}

.copyright {
  color: #999;
  font-size: 0.9rem;
}

.copyright p {
  margin-bottom: 0.5rem;
}

.tech-info a {
  color: #ccc;
  text-decoration: none;
  transition: var(--transition);
}

.tech-info a:hover {
  color: white;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--accent);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .footer-container {
    padding: 2rem 1rem 1rem;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }
  
  .stat-item {
    padding: 0.8rem;
  }
  
  .stat-number {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
