<template>
  <div class="manage-pets-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">
        <i class="fas fa-tasks"></i>
        宠物管理
      </h1>
      <router-link to="/manage/add" class="btn btn-primary">
        <i class="fas fa-plus"></i>
        添加新宠物
      </router-link>
    </div>

    <!-- 宠物表格 -->
    <div class="table-section">
      <div class="table-container">
        <table>
          <thead>
            <tr>
              <th>ID</th>
              <th>宠物名称</th>
              <th>种类</th>
              <th>品种</th>
              <th>年龄</th>
              <th>状态</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="pet in petsStore.pets" :key="pet.id">
              <td>{{ pet.id }}</td>
              <td>{{ pet.name }}</td>
              <td>{{ pet.species }}</td>
              <td>{{ pet.breed }}</td>
              <td>{{ pet.age }}岁</td>
              <td>
                <span v-if="!pet.is_adopted" class="status-badge status-available">待领养</span>
                <span v-else class="status-badge status-adopted">已被领养</span>
              </td>
              <td>
                <div class="action-buttons">
                  <router-link :to="'/pets/' + pet.id" class="btn btn-outline btn-sm">
                    <i class="fas fa-eye"></i>
                  </router-link>
                  <router-link :to="'/manage/edit/' + pet.id" class="btn btn-outline btn-sm">
                    <i class="fas fa-edit"></i>
                  </router-link>
                  <button class="btn btn-outline btn-sm btn-danger" @click="deletePet(pet.id)">
                    <i class="fas fa-trash-alt"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="petsStore.pets.length === 0" class="empty-state">
      <i class="fas fa-paw"></i>
      <h3>暂无宠物信息</h3>
      <p>点击上方按钮添加第一只宠物</p>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { usePetsStore } from '@/stores/pets'

const petsStore = usePetsStore()

// 删除宠物
const deletePet = async (petId) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这只宠物吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await petsStore.deletePet(petId)
    if (result.success) {
      ElMessage.success('宠物已删除')
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除宠物失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 获取宠物数据
const fetchPets = async () => {
  try {
    await petsStore.fetchPets()
  } catch (error) {
    console.error('获取宠物列表失败:', error)
    ElMessage.error('获取宠物列表失败')
  }
}

onMounted(() => {
  fetchPets()
})
</script>

<style scoped>
.manage-pets-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.page-title {
  font-size: 2rem;
  color: var(--secondary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.page-title i {
  color: var(--accent);
}

.btn {
  padding: 0.5rem 1.2rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
  font-size: 0.95rem;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
}

.btn-primary {
  background: var(--accent);
  color: white;
}

.btn-primary:hover {
  background: #e55a57;
  transform: translateY(-2px);
}

.btn-outline {
  background: transparent;
  border: 1px solid #ddd;
  color: #666;
}

.btn-outline:hover {
  background: #f5f5f5;
  border-color: #999;
}

.btn-sm {
  padding: 0.3rem 0.6rem;
  font-size: 0.85rem;
}

.btn-danger:hover {
  background: var(--danger);
  color: white;
  border-color: var(--danger);
}

.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: var(--card-shadow);
  overflow: hidden;
}

.table-container {
  overflow-x: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #eee;
}

th {
  background: var(--primary);
  color: white;
  font-weight: 500;
}

tr:hover {
  background: rgba(78, 137, 174, 0.05);
}

.status-badge {
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

.status-available {
  background: rgba(76, 175, 80, 0.15);
  color: var(--success);
}

.status-adopted {
  background: rgba(244, 67, 54, 0.15);
  color: var(--danger);
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.empty-state {
  text-align: center;
  padding: 3rem;
  color: #777;
}

.empty-state i {
  font-size: 4rem;
  margin-bottom: 1rem;
  color: #ddd;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .manage-pets-container {
    padding: 1rem;
  }
  
  .page-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}
</style>
