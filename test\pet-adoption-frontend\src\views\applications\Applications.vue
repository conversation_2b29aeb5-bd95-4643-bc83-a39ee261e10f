<template>
  <div class="applications-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">
        <i class="fas fa-file-alt"></i>
        我的申请
      </h1>
      <p class="page-subtitle">查看和管理您的宠物领养申请</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon pending">
            <i class="fas fa-clock"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.pending }}</div>
            <div class="stat-label">待审核</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon approved">
            <i class="fas fa-check-circle"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.approved }}</div>
            <div class="stat-label">已通过</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon rejected">
            <i class="fas fa-times-circle"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.rejected }}</div>
            <div class="stat-label">已拒绝</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon total">
            <i class="fas fa-list"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-label">总申请</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filter-section">
      <el-card class="filter-card">
        <div class="filter-content">
          <el-select
            v-model="statusFilter"
            placeholder="选择状态"
            clearable
            @change="handleFilterChange"
          >
            <el-option label="全部状态" value="" />
            <el-option label="待审核" value="pending" />
            <el-option label="已通过" value="approved" />
            <el-option label="已拒绝" value="rejected" />
          </el-select>
          
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleFilterChange"
          />
          
          <el-button @click="resetFilters">
            <i class="fas fa-undo"></i>
            重置
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 申请列表 -->
    <div class="applications-content" v-loading="loading">
      <div v-if="filteredApplications.length === 0 && !loading" class="empty-state">
        <i class="fas fa-inbox"></i>
        <h3>暂无申请记录</h3>
        <p>您还没有提交过任何领养申请</p>
        <el-button type="primary" @click="$router.push('/pets')">
          <i class="fas fa-search"></i>
          浏览宠物
        </el-button>
      </div>

      <div v-else class="applications-list">
        <el-card
          v-for="application in paginatedApplications"
          :key="application.id"
          class="application-card"
        >
          <div class="application-content">
            <!-- 宠物信息 -->
            <div class="pet-info">
              <div class="pet-avatar">
                <i :class="getPetIcon(application.pet?.species)"></i>
              </div>
              <div class="pet-details">
                <h3 class="pet-name">{{ application.pet?.name }}</h3>
                <p class="pet-breed">{{ application.pet?.breed }} · {{ application.pet?.age }}岁</p>
                <p class="application-date">
                  <i class="fas fa-calendar-alt"></i>
                  申请时间：{{ formatDate(application.create_time) }}
                </p>
              </div>
            </div>

            <!-- 申请状态 -->
            <div class="application-status">
              <el-tag
                :type="getStatusTagType(application.status)"
                size="large"
                effect="dark"
              >
                <i :class="getStatusIcon(application.status)"></i>
                {{ getStatusText(application.status, 'application') }}
              </el-tag>
              
              <div v-if="application.status === 'approved'" class="success-info">
                <p class="success-text">
                  <i class="fas fa-check-circle"></i>
                  恭喜！您的申请已通过审核
                </p>
                <p class="contact-info">
                  请联系我们完成领养手续：400-123-4567
                </p>
              </div>
              
              <div v-else-if="application.status === 'rejected'" class="rejection-info">
                <p class="rejection-text">
                  <i class="fas fa-info-circle"></i>
                  很遗憾，您的申请未通过审核
                </p>
                <p v-if="application.admin_notes" class="admin-notes">
                  原因：{{ application.admin_notes }}
                </p>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="application-actions">
              <el-button
                type="primary"
                size="small"
                @click="viewApplication(application)"
              >
                <i class="fas fa-eye"></i>
                查看详情
              </el-button>
              
              <el-button
                type="info"
                size="small"
                @click="viewPet(application.pet?.id)"
              >
                <i class="fas fa-paw"></i>
                查看宠物
              </el-button>
              
              <el-button
                v-if="application.status === 'pending'"
                type="danger"
                size="small"
                @click="cancelApplication(application)"
              >
                <i class="fas fa-times"></i>
                取消申请
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="filteredApplications.length > 0" class="pagination-section">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[5, 10, 20]"
        :total="filteredApplications.length"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 申请详情对话框 -->
    <ApplicationDetailDialog
      v-model="showDetailDialog"
      :application="selectedApplication"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { applicationApi } from '@/api/applications'
import { getPetIcon, getStatusText, formatDate } from '@/utils'
import ApplicationDetailDialog from '@/components/applications/ApplicationDetailDialog.vue'

const router = useRouter()
const authStore = useAuthStore()

// 状态
const loading = ref(true)
const applications = ref([])
const showDetailDialog = ref(false)
const selectedApplication = ref(null)

// 筛选
const statusFilter = ref('')
const dateRange = ref([])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)

// 统计数据
const stats = computed(() => {
  const pending = applications.value.filter(app => app.status === 'pending').length
  const approved = applications.value.filter(app => app.status === 'approved').length
  const rejected = applications.value.filter(app => app.status === 'rejected').length
  const total = applications.value.length

  return { pending, approved, rejected, total }
})

// 筛选后的申请
const filteredApplications = computed(() => {
  let result = applications.value

  // 状态筛选
  if (statusFilter.value) {
    result = result.filter(app => app.status === statusFilter.value)
  }

  // 日期筛选
  if (dateRange.value && dateRange.value.length === 2) {
    const [startDate, endDate] = dateRange.value
    result = result.filter(app => {
      const appDate = new Date(app.create_time)
      return appDate >= startDate && appDate <= endDate
    })
  }

  return result
})

// 分页后的申请
const paginatedApplications = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredApplications.value.slice(start, end)
})

// 获取状态标签类型
const getStatusTagType = (status) => {
  const typeMap = {
    'pending': 'warning',
    'approved': 'success',
    'rejected': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取状态图标
const getStatusIcon = (status) => {
  const iconMap = {
    'pending': 'fas fa-clock',
    'approved': 'fas fa-check-circle',
    'rejected': 'fas fa-times-circle'
  }
  return iconMap[status] || 'fas fa-question-circle'
}

// 获取用户申请列表
const fetchApplications = async () => {
  try {
    loading.value = true

    // 检查用户是否已登录
    if (!authStore.isAuthenticated) {
      ElMessage.error('请先登录')
      router.push('/login')
      return
    }

    const result = await applicationApi.getMyApplications({
      page: 1,
      size: 100 // 获取所有申请，前端分页
    })

    // 处理分页响应
    console.log('获取申请列表响应:', result)
    if (result && result.data && Array.isArray(result.data)) {
      applications.value = result.data
    } else if (Array.isArray(result)) {
      applications.value = result
    } else {
      applications.value = []
      console.log('申请列表数据格式不正确:', result)
    }
  } catch (error) {
    console.error('获取申请列表失败:', error)

    // 检查是否是认证错误
    if (error.response?.status === 401) {
      ElMessage.error('登录已过期，请重新登录')
      authStore.logout()
      router.push('/login')
    } else {
      ElMessage.error(error.response?.data?.message || '获取申请列表失败')
    }
  } finally {
    loading.value = false
  }
}

// 筛选变化
const handleFilterChange = () => {
  currentPage.value = 1
}

// 重置筛选
const resetFilters = () => {
  statusFilter.value = ''
  dateRange.value = []
  currentPage.value = 1
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

// 查看申请详情
const viewApplication = (application) => {
  selectedApplication.value = application
  showDetailDialog.value = true
}

// 查看宠物
const viewPet = (petId) => {
  if (petId) {
    router.push(`/pets/${petId}`)
  }
}

// 取消申请
const cancelApplication = async (application) => {
  try {
    await ElMessageBox.confirm(
      '确定要取消这个申请吗？取消后无法恢复。',
      '确认取消',
      {
        confirmButtonText: '确定取消',
        cancelButtonText: '保留申请',
        type: 'warning'
      }
    )

    await applicationApi.cancelApplication(application.id)
    ElMessage.success('申请已取消')
    
    // 从列表中移除
    const index = applications.value.findIndex(app => app.id === application.id)
    if (index !== -1) {
      applications.value.splice(index, 1)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消申请失败:', error)
      ElMessage.error('取消申请失败')
    }
  }
}

onMounted(() => {
  fetchApplications()
})
</script>

<style scoped>
.applications-container {
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 2rem;
}

.page-title {
  font-size: 2rem;
  color: var(--secondary);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.page-title i {
  color: var(--accent);
}

.page-subtitle {
  color: #666;
  font-size: 1.1rem;
}

.stats-section {
  margin-bottom: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: var(--card-shadow);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: var(--transition);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.stat-icon.pending {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.stat-icon.approved {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.stat-icon.rejected {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.stat-icon.total {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
}

.stat-number {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--secondary);
  margin-bottom: 0.2rem;
}

.stat-label {
  color: #666;
  font-size: 0.9rem;
}

.filter-section {
  margin-bottom: 2rem;
}

.filter-card {
  border-radius: 8px;
  box-shadow: var(--card-shadow);
}

.filter-content {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.applications-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.application-card {
  border-radius: 8px;
  box-shadow: var(--card-shadow);
  transition: var(--transition);
}

.application-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.application-content {
  display: flex;
  gap: 1.5rem;
  align-items: flex-start;
}

.pet-info {
  display: flex;
  gap: 1rem;
  flex: 1;
}

.pet-avatar {
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #64b3f4, #c2e59c);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.pet-details {
  flex: 1;
}

.pet-name {
  font-size: 1.2rem;
  color: var(--secondary);
  margin-bottom: 0.3rem;
}

.pet-breed {
  color: #666;
  margin-bottom: 0.5rem;
}

.application-date {
  color: #999;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.application-status {
  flex-shrink: 0;
  text-align: center;
  min-width: 150px;
}

.success-info,
.rejection-info {
  margin-top: 0.8rem;
  padding: 0.8rem;
  border-radius: 4px;
  font-size: 0.9rem;
}

.success-info {
  background: rgba(39, 174, 96, 0.1);
  border: 1px solid rgba(39, 174, 96, 0.2);
}

.rejection-info {
  background: rgba(231, 76, 60, 0.1);
  border: 1px solid rgba(231, 76, 60, 0.2);
}

.success-text,
.rejection-text {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.success-text {
  color: #27ae60;
}

.rejection-text {
  color: #e74c3c;
}

.contact-info,
.admin-notes {
  color: #666;
  font-size: 0.8rem;
  line-height: 1.4;
}

.application-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex-shrink: 0;
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .filter-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .application-content {
    flex-direction: column;
  }
  
  .application-status {
    min-width: auto;
    text-align: left;
  }
  
  .application-actions {
    flex-direction: row;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .pet-info {
    flex-direction: column;
    text-align: center;
  }
  
  .application-actions {
    flex-direction: column;
  }
}
</style>
