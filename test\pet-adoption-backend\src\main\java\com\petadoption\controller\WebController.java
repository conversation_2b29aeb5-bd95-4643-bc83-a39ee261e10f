package com.petadoption.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.petadoption.entity.Pet;
import com.petadoption.mapper.ApplicationMapper;
import com.petadoption.mapper.PetMapper;
import com.petadoption.mapper.UserMapper;
import com.petadoption.service.MybatisPetService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Web页面控制器
 * 处理Thymeleaf模板页面请求
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Controller
@RequestMapping("/web")
@RequiredArgsConstructor
public class WebController {

    private final MybatisPetService mybatisPetService;
    private final PetMapper petMapper;
    private final UserMapper userMapper;
    private final ApplicationMapper applicationMapper;

    /**
     * 首页
     */
    @GetMapping({"", "/"})
    public String index(Model model) {
        log.info("访问首页");
        
        try {
            // 获取统计数据
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalPets", petMapper.selectCount(null));
            stats.put("availablePets", mybatisPetService.countAvailablePets());
            stats.put("adoptedPets", mybatisPetService.countAdoptedPets());
            stats.put("totalUsers", userMapper.selectCount(null));
            
            // 获取最新宠物（最多6只）
            List<Pet> latestPets = mybatisPetService.getLatestPets(6);
            
            model.addAttribute("stats", stats);
            model.addAttribute("latestPets", latestPets);
            model.addAttribute("title", "首页 - 宠物领养系统");
            
        } catch (Exception e) {
            log.error("获取首页数据失败", e);
            // 设置默认值
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalPets", 0);
            stats.put("availablePets", 0);
            stats.put("adoptedPets", 0);
            stats.put("totalUsers", 0);
            model.addAttribute("stats", stats);
        }
        
        return "index";
    }

    /**
     * 宠物列表页面
     */
    @GetMapping("/pets")
    public String petList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "12") int size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String species,
            @RequestParam(required = false) String gender,
            @RequestParam(required = false) String ageRange,
            @RequestParam(required = false) String status,
            Model model) {
        
        log.info("访问宠物列表页面 - 页码: {}, 关键词: {}, 种类: {}", page, keyword, species);
        
        try {
            IPage<Pet> pets;
            
            // 根据搜索条件查询
            if (keyword != null && !keyword.trim().isEmpty()) {
                pets = mybatisPetService.searchPetsByKeyword(keyword.trim(), page, size);
            } else {
                // 解析年龄范围
                Integer minAge = null, maxAge = null;
                if (ageRange != null && !ageRange.isEmpty()) {
                    String[] ages = ageRange.split("-");
                    if (ages.length == 2) {
                        try {
                            minAge = Integer.parseInt(ages[0]);
                            maxAge = Integer.parseInt(ages[1]);
                        } catch (NumberFormatException e) {
                            log.warn("年龄范围格式错误: {}", ageRange);
                        }
                    }
                }
                
                // 解析状态
                Boolean isAdopted = null;
                if ("available".equals(status)) {
                    isAdopted = false;
                } else if ("adopted".equals(status)) {
                    isAdopted = true;
                }
                
                pets = mybatisPetService.searchPets(species, null, minAge, maxAge, gender, isAdopted, page, size);
            }
            
            model.addAttribute("pets", pets);
            model.addAttribute("title", "宠物列表 - 宠物领养系统");
            
        } catch (Exception e) {
            log.error("获取宠物列表失败", e);
            model.addAttribute("error", "获取宠物列表失败，请稍后重试");
        }
        
        return "pets/list";
    }

    /**
     * 宠物详情页面
     */
    @GetMapping("/pets/{id}")
    public String petDetail(@PathVariable Long id, Model model) {
        log.info("访问宠物详情页面 - ID: {}", id);
        
        try {
            Pet pet = mybatisPetService.getById(id);
            if (pet == null) {
                model.addAttribute("error", "宠物不存在");
                return "error/404";
            }
            
            // 获取推荐宠物
            List<Pet> recommendedPets = mybatisPetService.getRecommendedPets(pet.getSpecies(), id, 3);
            
            model.addAttribute("pet", pet);
            model.addAttribute("recommendedPets", recommendedPets);
            model.addAttribute("title", pet.getName() + " - 宠物详情");
            
        } catch (Exception e) {
            log.error("获取宠物详情失败 - ID: {}", id, e);
            model.addAttribute("error", "获取宠物详情失败，请稍后重试");
        }
        
        return "pets/detail";
    }

    /**
     * 关于我们页面
     */
    @GetMapping("/about")
    public String about(Model model) {
        model.addAttribute("title", "关于我们 - 宠物领养系统");
        return "about";
    }

    /**
     * 登录页面
     */
    @GetMapping("/login")
    public String login(Model model) {
        model.addAttribute("title", "登录 - 宠物领养系统");
        return "auth/login";
    }

    /**
     * 注册页面
     */
    @GetMapping("/register")
    public String register(Model model) {
        model.addAttribute("title", "注册 - 宠物领养系统");
        return "auth/register";
    }

    /**
     * 申请领养页面
     */
    @GetMapping("/applications/create")
    public String createApplication(@RequestParam Long petId, Model model) {
        log.info("访问申请领养页面 - 宠物ID: {}", petId);
        
        try {
            Pet pet = mybatisPetService.getById(petId);
            if (pet == null) {
                model.addAttribute("error", "宠物不存在");
                return "error/404";
            }
            
            if (pet.getIsAdopted()) {
                model.addAttribute("error", "该宠物已被领养");
                return "error/400";
            }
            
            model.addAttribute("pet", pet);
            model.addAttribute("title", "申请领养 " + pet.getName() + " - 宠物领养系统");
            
        } catch (Exception e) {
            log.error("获取申请领养页面失败 - 宠物ID: {}", petId, e);
            model.addAttribute("error", "页面加载失败，请稍后重试");
        }
        
        return "applications/create";
    }
}
