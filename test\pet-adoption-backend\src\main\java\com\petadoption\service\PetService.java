package com.petadoption.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.petadoption.dto.request.PetCreateRequest;
import com.petadoption.dto.request.PetUpdateRequest;
import com.petadoption.dto.response.PetResponse;
import com.petadoption.entity.Pet;
import com.petadoption.exception.BusinessException;
import com.petadoption.mapper.PetMapper;
import com.petadoption.util.PageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 宠物服务
 * 处理宠物相关的业务逻辑
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PetService {

    private final PetMapper petMapper;
    
    /**
     * 创建宠物
     * 
     * @param request 创建请求
     * @return 宠物响应
     */
    @Transactional
    public PetResponse createPet(PetCreateRequest request) {
        log.info("创建宠物: {}", request.getName());
        
        // 创建宠物实体
        Pet pet = new Pet();
        pet.setName(request.getName());
        pet.setSpecies(request.getSpecies());
        pet.setBreed(request.getBreed());
        pet.setAge(request.getAge());
        pet.setGender(request.getGender());
        pet.setWeight(request.getWeight());
        pet.setColor(request.getColor());
        pet.setHealthStatus(request.getHealthStatus());
        pet.setVaccinationStatus(request.getVaccinationStatus());
        pet.setIsNeutered(request.getIsNeutered());
        pet.setDescription(request.getDescription());
        pet.setImageUrl(request.getImageUrl());
        pet.setRescueLocation(request.getRescueLocation());
        pet.setRescueTime(request.getRescueTime());
        pet.setSpecialNeeds(request.getSpecialNeeds());
        pet.setPersonality(request.getPersonality());
        pet.setGoodWithKids(request.getGoodWithKids());
        pet.setGoodWithPets(request.getGoodWithPets());
        pet.setActivityLevel(request.getActivityLevel());
        pet.setTrainingLevel(request.getTrainingLevel());
        
        // 保存宠物
        petMapper.insert(pet);
        
        log.info("宠物创建成功: {} (ID: {})", pet.getName(), pet.getId());
        
        return convertToResponse(pet);
    }
    
    /**
     * 更新宠物信息
     * 
     * @param petId 宠物ID
     * @param request 更新请求
     * @return 宠物响应
     */
    @Transactional
    public PetResponse updatePet(Long petId, PetUpdateRequest request) {
        log.info("更新宠物信息: ID {}", petId);
        
        // 查找宠物
        Pet pet = petMapper.selectById(petId);
        if (pet == null) {
            throw BusinessException.petNotFound();
        }
        
        // 更新字段
        if (request.getName() != null) {
            pet.setName(request.getName());
        }
        if (request.getSpecies() != null) {
            pet.setSpecies(request.getSpecies());
        }
        if (request.getBreed() != null) {
            pet.setBreed(request.getBreed());
        }
        if (request.getAge() != null) {
            pet.setAge(request.getAge());
        }
        if (request.getGender() != null) {
            pet.setGender(request.getGender());
        }
        if (request.getWeight() != null) {
            pet.setWeight(request.getWeight());
        }
        if (request.getColor() != null) {
            pet.setColor(request.getColor());
        }
        if (request.getHealthStatus() != null) {
            pet.setHealthStatus(request.getHealthStatus());
        }
        if (request.getVaccinationStatus() != null) {
            pet.setVaccinationStatus(request.getVaccinationStatus());
        }
        if (request.getIsNeutered() != null) {
            pet.setIsNeutered(request.getIsNeutered());
        }
        if (request.getDescription() != null) {
            pet.setDescription(request.getDescription());
        }
        if (request.getImageUrl() != null) {
            pet.setImageUrl(request.getImageUrl());
        }
        if (request.getSpecialNeeds() != null) {
            pet.setSpecialNeeds(request.getSpecialNeeds());
        }
        if (request.getPersonality() != null) {
            pet.setPersonality(request.getPersonality());
        }
        if (request.getGoodWithKids() != null) {
            pet.setGoodWithKids(request.getGoodWithKids());
        }
        if (request.getGoodWithPets() != null) {
            pet.setGoodWithPets(request.getGoodWithPets());
        }
        if (request.getActivityLevel() != null) {
            pet.setActivityLevel(request.getActivityLevel());
        }
        if (request.getTrainingLevel() != null) {
            pet.setTrainingLevel(request.getTrainingLevel());
        }
        
        // 保存更新
        petMapper.updateById(pet);
        
        log.info("宠物信息更新成功: {} (ID: {})", pet.getName(), pet.getId());
        
        return convertToResponse(pet);
    }
    
    /**
     * 删除宠物
     * 
     * @param petId 宠物ID
     */
    @Transactional
    public void deletePet(Long petId) {
        log.info("删除宠物: ID {}", petId);
        
        // 检查宠物是否存在
        Pet pet = petMapper.selectById(petId);
        if (pet == null) {
            throw BusinessException.petNotFound();
        }

        // 检查是否已被领养
        if (pet.getIsAdopted()) {
            throw new BusinessException("已被领养的宠物不能删除");
        }

        // 删除宠物
        petMapper.deleteById(petId);
        
        log.info("宠物删除成功: {} (ID: {})", pet.getName(), pet.getId());
    }
    
    /**
     * 根据ID获取宠物详情
     * 
     * @param petId 宠物ID
     * @return 宠物响应
     */
    @Transactional(readOnly = true)
    public PetResponse getPetById(Long petId) {
        Pet pet = petMapper.selectById(petId);
        if (pet == null) {
            throw BusinessException.petNotFound();
        }

        return convertToResponse(pet);
    }
    
    /**
     * 分页查询宠物列表
     *
     * @param page 页码
     * @param size 页面大小
     * @param species 种类筛选
     * @param isAdopted 领养状态筛选
     * @param keyword 关键词搜索
     * @param sortBy 排序字段
     * @param sortDir 排序方向
     * @return 分页响应
     */
    @Transactional(readOnly = true)
    public PageUtil.PageResponse<PetResponse> getPets(
            Integer page, Integer size, String species, Boolean isAdopted,
            String keyword, String sortBy, String sortDir) {

        // 创建分页对象
        Page<Pet> pageObj = new Page<>(page != null ? page : 1, size != null ? size : 10);

        // 创建查询条件
        QueryWrapper<Pet> queryWrapper = new QueryWrapper<>();

        // 种类筛选
        if (species != null && !species.trim().isEmpty()) {
            queryWrapper.eq("species", species);
        }

        // 领养状态筛选
        if (isAdopted != null) {
            queryWrapper.eq("is_adopted", isAdopted);
        }

        // 关键词搜索
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.and(wrapper -> wrapper
                .like("name", keyword)
                .or()
                .like("breed", keyword)
                .or()
                .like("description", keyword)
            );
        }

        // 排序 - 将前端的驼峰命名转换为数据库字段名
        String orderBy = convertSortFieldToDbColumn(sortBy != null ? sortBy : "create_time");
        if ("desc".equalsIgnoreCase(sortDir)) {
            queryWrapper.orderByDesc(orderBy);
        } else {
            queryWrapper.orderByAsc(orderBy);
        }

        // 执行查询
        IPage<Pet> petPage = petMapper.selectPage(pageObj, queryWrapper);

        // 转换响应
        List<PetResponse> petResponses = petPage.getRecords().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());

        // 创建分页响应
        PageUtil.PageResponse<PetResponse> response = new PageUtil.PageResponse<>();
        response.setData(petResponses);
        response.setTotal(petPage.getTotal());
        response.setPage((int) petPage.getCurrent());
        response.setSize((int) petPage.getSize());
        response.setTotalPages((int) petPage.getPages());
        response.setHasNext(petPage.getCurrent() < petPage.getPages());
        response.setHasPrevious(petPage.getCurrent() > 1);

        return response;
    }
    
    /**
     * 获取最新宠物列表
     * 
     * @param limit 数量限制
     * @return 宠物列表
     */
    @Transactional(readOnly = true)
    public List<PetResponse> getLatestPets(Integer limit) {
        List<Pet> pets = petMapper.findLatestAvailablePets(limit != null ? limit : 6);

        return pets.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取推荐宠物列表
     * 
     * @param petId 当前宠物ID
     * @param limit 数量限制
     * @return 推荐宠物列表
     */
    @Transactional(readOnly = true)
    public List<PetResponse> getRecommendedPets(Long petId, Integer limit) {
        // 获取当前宠物信息
        Pet currentPet = petMapper.selectById(petId);
        if (currentPet == null) {
            throw BusinessException.petNotFound();
        }

        // 查找同种类的其他宠物
        List<Pet> pets = petMapper.findRecommendedPets(
                currentPet.getSpecies(), petId, limit != null ? limit : 4);

        return pets.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }
    
    /**
     * 更新宠物领养状态
     * 
     * @param petId 宠物ID
     * @param isAdopted 是否已领养
     * @param adopterId 领养者ID
     */
    @Transactional
    public void updateAdoptionStatus(Long petId, Boolean isAdopted, Long adopterId) {
        log.info("更新宠物领养状态: ID {}, 已领养: {}, 领养者: {}", petId, isAdopted, adopterId);

        Pet pet = petMapper.selectById(petId);
        if (pet == null) {
            throw BusinessException.petNotFound();
        }

        if (isAdopted) {
            pet.setAdopted(adopterId);
        } else {
            pet.cancelAdoption();
        }

        petMapper.updateById(pet);

        log.info("宠物领养状态更新成功: {} (ID: {})", pet.getName(), pet.getId());
    }
    

    
    /**
     * 转换为响应对象
     */
    private PetResponse convertToResponse(Pet pet) {
        return PetResponse.builder()
                .id(pet.getId())
                .name(pet.getName())
                .species(pet.getSpecies())
                .breed(pet.getBreed())
                .age(pet.getAge())
                .gender(pet.getGender())
                .weight(pet.getWeight())
                .color(pet.getColor())
                .healthStatus(pet.getHealthStatus())
                .vaccinationStatus(pet.getVaccinationStatus())
                .isNeutered(pet.getIsNeutered())
                .description(pet.getDescription())
                .imageUrl(pet.getImageUrl())
                .isAdopted(pet.getIsAdopted())
                .adopterId(pet.getAdopterId())
                .adoptionTime(pet.getAdoptionTime())
                .rescueLocation(pet.getRescueLocation())
                .rescueTime(pet.getRescueTime())
                .specialNeeds(pet.getSpecialNeeds())
                .personality(pet.getPersonality())
                .goodWithKids(pet.getGoodWithKids())
                .goodWithPets(pet.getGoodWithPets())
                .activityLevel(pet.getActivityLevel())
                .trainingLevel(pet.getTrainingLevel())
                .createTime(pet.getCreateTime())
                .updateTime(pet.getUpdateTime())
                .build();
    }

    /**
     * 将前端的驼峰命名字段转换为数据库字段名
     *
     * @param sortField 前端排序字段
     * @return 数据库字段名
     */
    private String convertSortFieldToDbColumn(String sortField) {
        if (sortField == null) {
            return "create_time";
        }

        switch (sortField) {
            case "createTime":
                return "create_time";
            case "updateTime":
                return "update_time";
            case "adoptionTime":
                return "adoption_time";
            case "rescueTime":
                return "rescue_time";
            case "isAdopted":
                return "is_adopted";
            case "isNeutered":
                return "is_neutered";
            case "adopterId":
                return "adopter_id";
            case "healthStatus":
                return "health_status";
            case "vaccinationStatus":
                return "vaccination_status";
            case "rescueLocation":
                return "rescue_location";
            case "specialNeeds":
                return "special_needs";
            case "goodWithKids":
                return "good_with_kids";
            case "goodWithPets":
                return "good_with_pets";
            case "activityLevel":
                return "activity_level";
            case "trainingLevel":
                return "training_level";
            case "imageUrl":
                return "image_url";
            default:
                // 对于已经是数据库字段名的情况，直接返回
                return sortField;
        }
    }
}
