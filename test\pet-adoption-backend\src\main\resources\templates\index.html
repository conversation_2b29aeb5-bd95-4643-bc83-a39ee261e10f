<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" th:replace="~{layout/base :: html}">
<head>
    <title>首页 - 宠物领养系统</title>
</head>
<body>
    <th:block th:fragment="content">
        <!-- 英雄区域 -->
        <section class="hero-section">
            <div class="container text-center">
                <h1 class="display-4 fw-bold mb-4">为流浪动物寻找温暖的家</h1>
                <p class="lead mb-4">每一个生命都值得被爱护，每一个家庭都可以因为宠物而更加温暖</p>
                <a href="/web/pets" class="btn btn-light btn-lg me-3">
                    <i class="fas fa-search me-2"></i>浏览宠物
                </a>
                <a href="/web/about" class="btn btn-outline-light btn-lg">
                    <i class="fas fa-info-circle me-2"></i>了解更多
                </a>
            </div>
        </section>

        <!-- 统计数据 -->
        <section class="py-5">
            <div class="container">
                <div class="row text-center">
                    <div class="col-md-3 mb-4">
                        <div class="card border-0 h-100">
                            <div class="card-body">
                                <i class="fas fa-paw feature-icon"></i>
                                <h3 class="fw-bold" th:text="${stats.totalPets} ?: '0'">0</h3>
                                <p class="text-muted">总宠物数量</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="card border-0 h-100">
                            <div class="card-body">
                                <i class="fas fa-heart feature-icon text-success"></i>
                                <h3 class="fw-bold" th:text="${stats.adoptedPets} ?: '0'">0</h3>
                                <p class="text-muted">已被领养</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="card border-0 h-100">
                            <div class="card-body">
                                <i class="fas fa-home feature-icon text-warning"></i>
                                <h3 class="fw-bold" th:text="${stats.availablePets} ?: '0'">0</h3>
                                <p class="text-muted">等待领养</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="card border-0 h-100">
                            <div class="card-body">
                                <i class="fas fa-users feature-icon text-info"></i>
                                <h3 class="fw-bold" th:text="${stats.totalUsers} ?: '0'">0</h3>
                                <p class="text-muted">注册用户</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 最新宠物 -->
        <section class="py-5 bg-light">
            <div class="container">
                <div class="text-center mb-5">
                    <h2 class="fw-bold">最新加入的小伙伴</h2>
                    <p class="text-muted">这些可爱的小家伙正在等待一个温暖的家</p>
                </div>
                
                <div class="row" th:if="${latestPets != null and !latestPets.isEmpty()}">
                    <div class="col-md-4 mb-4" th:each="pet : ${latestPets}">
                        <div class="card pet-card h-100">
                            <div class="position-relative">
                                <img th:src="${pet.imageUrl} ?: '/images/default-pet.jpg'" 
                                     class="card-img-top pet-image" 
                                     th:alt="${pet.name}">
                                <span class="badge bg-success status-badge" th:if="${!pet.isAdopted}">
                                    可领养
                                </span>
                                <span class="badge bg-secondary status-badge" th:if="${pet.isAdopted}">
                                    已领养
                                </span>
                            </div>
                            <div class="card-body">
                                <h5 class="card-title" th:text="${pet.name}">宠物名称</h5>
                                <p class="text-muted mb-2">
                                    <i class="fas fa-tag me-1"></i>
                                    <span th:text="${pet.species}">种类</span> · 
                                    <span th:text="${pet.breed}">品种</span>
                                </p>
                                <p class="text-muted mb-2">
                                    <i class="fas fa-birthday-cake me-1"></i>
                                    <span th:text="${pet.age} + '岁'">年龄</span> · 
                                    <i class="fas fa-venus-mars ms-2 me-1"></i>
                                    <span th:text="${pet.gender}">性别</span>
                                </p>
                                <p class="card-text" th:text="${#strings.abbreviate(pet.description, 80)}">
                                    宠物描述...
                                </p>
                            </div>
                            <div class="card-footer bg-transparent">
                                <a th:href="@{/web/pets/{id}(id=${pet.id})}" 
                                   class="btn btn-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i>查看详情
                                </a>
                                <a th:href="@{/web/applications/create(petId=${pet.id})}" 
                                   class="btn btn-outline-success btn-sm ms-2"
                                   th:if="${!pet.isAdopted}">
                                    <i class="fas fa-heart me-1"></i>申请领养
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center" th:if="${latestPets == null or latestPets.isEmpty()}">
                    <p class="text-muted">暂时没有新的宠物信息</p>
                </div>
                
                <div class="text-center mt-4">
                    <a href="/web/pets" class="btn btn-primary">
                        <i class="fas fa-paw me-2"></i>查看所有宠物
                    </a>
                </div>
            </div>
        </section>

        <!-- 特色功能 -->
        <section class="py-5">
            <div class="container">
                <div class="text-center mb-5">
                    <h2 class="fw-bold">为什么选择我们</h2>
                    <p class="text-muted">我们致力于为每一个小生命找到最合适的家庭</p>
                </div>
                
                <div class="row">
                    <div class="col-md-4 text-center mb-4">
                        <i class="fas fa-shield-alt feature-icon text-primary"></i>
                        <h4>安全可靠</h4>
                        <p class="text-muted">严格的审核流程，确保每一次领养都是负责任的选择</p>
                    </div>
                    <div class="col-md-4 text-center mb-4">
                        <i class="fas fa-heart feature-icon text-danger"></i>
                        <h4>专业关爱</h4>
                        <p class="text-muted">专业的宠物护理团队，为每只宠物提供最好的照顾</p>
                    </div>
                    <div class="col-md-4 text-center mb-4">
                        <i class="fas fa-handshake feature-icon text-success"></i>
                        <h4>持续支持</h4>
                        <p class="text-muted">领养后的跟踪服务，帮助您和宠物更好地适应新生活</p>
                    </div>
                </div>
            </div>
        </section>
    </th:block>
</body>
</html>
