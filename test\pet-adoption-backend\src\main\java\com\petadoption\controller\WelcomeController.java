package com.petadoption.controller;

import com.petadoption.common.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 欢迎页面控制器
 * 提供公开访问的基本信息
 * 
 * <AUTHOR> Team
 */
@RestController
public class WelcomeController {
    
    /**
     * 首页欢迎信息
     */
    @GetMapping("/")
    public Result<Map<String, Object>> welcome() {
        Map<String, Object> data = new HashMap<>();
        data.put("message", "欢迎使用宠物领养系统API");
        data.put("version", "1.0.0");
        data.put("timestamp", LocalDateTime.now());
        data.put("status", "running");
        data.put("endpoints", Map.of(
            "登录", "/auth/login",
            "注册", "/auth/register", 
            "宠物列表", "/pets",
            "API文档", "/swagger-ui.html"
        ));
        
        return Result.success("系统运行正常", data);
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> health() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "UP");
        data.put("timestamp", LocalDateTime.now());
        data.put("service", "pet-adoption-backend");
        
        return Result.success("服务健康", data);
    }
    
    /**
     * API信息
     */
    @GetMapping("/api/info")
    public Result<Map<String, Object>> apiInfo() {
        Map<String, Object> data = new HashMap<>();
        data.put("name", "宠物领养系统API");
        data.put("version", "1.0.0");
        data.put("description", "提供宠物信息管理、用户认证、领养申请等功能");
        data.put("author", "PetAdoption Team");
        data.put("contact", "<EMAIL>");
        
        return Result.success("API信息", data);
    }
}
