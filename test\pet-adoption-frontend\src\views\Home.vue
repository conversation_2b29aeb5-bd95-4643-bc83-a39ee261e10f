<template>
  <div class="home-container">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">
            <i class="fas fa-paw"></i>
            欢迎来到宠物领养中心
          </h1>
          <p class="hero-subtitle">
            我们致力于为流浪宠物寻找温暖的家，提供安全可靠的领养平台。
            在这里，您可以找到最适合的毛孩子，给无家可归的小动物一个充满爱的家。
          </p>
          <div class="hero-actions">
            <router-link to="/pets" class="btn btn-primary btn-large">
              <i class="fas fa-search"></i>
              浏览待领养宠物
            </router-link>
            <router-link 
              v-if="!authStore.isAuthenticated" 
              to="/register" 
              class="btn btn-outline btn-large"
            >
              <i class="fas fa-user-plus"></i>
              立即注册
            </router-link>
          </div>
        </div>
        <div class="hero-image">
          <div class="image-placeholder">
            <i class="fas fa-heart"></i>
            <span>爱心领养</span>
          </div>
        </div>
      </div>
    </section>

    <!-- 统计数据 -->
    <section class="stats-section">
      <div class="stats-container">
        <div class="stat-item">
          <div class="stat-icon">
            <i class="fas fa-paw"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.totalPets }}</div>
            <div class="stat-label">救助宠物</div>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon">
            <i class="fas fa-home"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.adoptedPets }}</div>
            <div class="stat-label">成功领养</div>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon">
            <i class="fas fa-users"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.totalUsers }}</div>
            <div class="stat-label">注册用户</div>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon">
            <i class="fas fa-heart"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.happyFamilies }}</div>
            <div class="stat-label">幸福家庭</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 最新宠物 -->
    <section class="latest-pets-section">
      <div class="section-header">
        <h2 class="section-title">
          <i class="fas fa-star"></i>
          最新待领养宠物
        </h2>
        <p class="section-subtitle">这些可爱的小家伙正在等待一个温暖的家</p>
      </div>
      
      <div class="pets-grid" v-loading="petsStore.loading">
        <div 
          v-for="pet in latestPets" 
          :key="pet.id" 
          class="pet-card"
          @click="$router.push(`/pets/${pet.id}`)"
        >
          <div class="pet-image">
            <i :class="getPetIcon(pet.species)"></i>
          </div>
          <div class="pet-info">
            <h3 class="pet-name">{{ pet.name }}</h3>
            <div class="pet-meta">
              <span class="pet-breed">
                <i class="fas fa-tag"></i>
                {{ pet.breed }}
              </span>
              <span class="pet-age">
                <i class="fas fa-birthday-cake"></i>
                {{ pet.age }}岁
              </span>
            </div>
            <p class="pet-description">{{ pet.description.substring(0, 50) }}...</p>
            <div class="pet-status">
              <span class="status-badge status-available">待领养</span>
            </div>
          </div>
        </div>
      </div>

      <div class="section-footer">
        <router-link to="/pets" class="btn btn-outline">
          <i class="fas fa-arrow-right"></i>
          查看更多宠物
        </router-link>
      </div>
    </section>

    <!-- 领养流程 -->
    <section class="process-section">
      <div class="section-header">
        <h2 class="section-title">
          <i class="fas fa-route"></i>
          领养流程
        </h2>
        <p class="section-subtitle">简单四步，开启您的爱心之旅</p>
      </div>
      
      <div class="process-steps">
        <div class="step-item">
          <div class="step-number">1</div>
          <div class="step-icon">
            <i class="fas fa-search"></i>
          </div>
          <h3 class="step-title">浏览宠物</h3>
          <p class="step-description">在我们的平台上浏览各种可爱的宠物，找到您心仪的小伙伴</p>
        </div>
        <div class="step-item">
          <div class="step-number">2</div>
          <div class="step-icon">
            <i class="fas fa-file-alt"></i>
          </div>
          <h3 class="step-title">提交申请</h3>
          <p class="step-description">填写领养申请表，详细说明您的养宠条件和经验</p>
        </div>
        <div class="step-item">
          <div class="step-number">3</div>
          <div class="step-icon">
            <i class="fas fa-user-check"></i>
          </div>
          <h3 class="step-title">审核评估</h3>
          <p class="step-description">我们的工作人员会仔细审核您的申请，确保宠物的安全</p>
        </div>
        <div class="step-item">
          <div class="step-number">4</div>
          <div class="step-icon">
            <i class="fas fa-home"></i>
          </div>
          <h3 class="step-title">带回家</h3>
          <p class="step-description">审核通过后，您就可以带着新家庭成员回家啦！</p>
        </div>
      </div>
    </section>

    <!-- 成功故事 -->
    <section class="success-stories-section">
      <div class="section-header">
        <h2 class="section-title">
          <i class="fas fa-heart"></i>
          成功故事
        </h2>
        <p class="section-subtitle">每一个成功的领养都是一个温暖的故事</p>
      </div>
      
      <div class="stories-grid">
        <div v-for="story in successStories" :key="story.id" class="story-card">
          <div class="story-image">
            <i :class="getPetIcon(story.petSpecies)"></i>
          </div>
          <div class="story-content">
            <h3 class="story-title">{{ story.title }}</h3>
            <p class="story-text">{{ story.content }}</p>
            <div class="story-meta">
              <span class="story-author">{{ story.author }}</span>
              <span class="story-date">{{ formatDate(story.date) }}</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { usePetsStore } from '@/stores/pets'
import { getPetIcon, formatDate } from '@/utils'

const authStore = useAuthStore()
const petsStore = usePetsStore()

// 统计数据
const stats = ref({
  totalPets: 0,
  adoptedPets: 0,
  totalUsers: 0,
  happyFamilies: 0
})

// 最新宠物（取前6个）
const latestPets = computed(() => {
  return petsStore.availablePets.slice(0, 6)
})

// 成功故事
const successStories = ref([
  {
    id: 1,
    title: '豆豆找到了新家',
    content: '感谢宠物领养中心，让我遇到了豆豆。它现在是我们家最重要的成员，每天都给我们带来无尽的快乐。',
    author: '张女士',
    date: '2023-11-15',
    petSpecies: '狗'
  },
  {
    id: 2,
    title: '咪咪的幸福生活',
    content: '咪咪刚来的时候很胆小，现在已经完全适应了新环境，每天都会在阳台上晒太阳，非常可爱。',
    author: '李先生',
    date: '2023-11-10',
    petSpecies: '猫'
  },
  {
    id: 3,
    title: '小白的新开始',
    content: '小白是我们家第一只宠物，孩子们都很喜欢它。现在它已经成为了孩子们最好的玩伴。',
    author: '王女士',
    date: '2023-11-05',
    petSpecies: '兔'
  }
])

// 获取统计数据
const fetchStats = async () => {
  try {
    // 这里可以调用API获取真实统计数据
    // const response = await statsApi.getStats()
    // stats.value = response
    
    // 模拟数据
    stats.value = {
      totalPets: 156,
      adoptedPets: 89,
      totalUsers: 234,
      happyFamilies: 89
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 获取最新宠物
const fetchLatestPets = async () => {
  try {
    await petsStore.fetchPets({ size: 6 })
  } catch (error) {
    console.error('获取最新宠物失败:', error)
  }
}

onMounted(() => {
  fetchStats()
  fetchLatestPets()
})
</script>

<style scoped>
.home-container {
  width: 100%;
}

/* 英雄区域 */
.hero-section {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  padding: 4rem 0;
  margin-bottom: 4rem;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
}

.hero-title {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.hero-title i {
  color: var(--accent);
}

.hero-subtitle {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.1rem;
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-placeholder {
  width: 300px;
  height: 300px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  border: 3px dashed rgba(255, 255, 255, 0.3);
}

.image-placeholder i {
  font-size: 4rem;
  color: var(--accent);
}

.image-placeholder span {
  font-size: 1.2rem;
  font-weight: 600;
}

/* 统计数据 */
.stats-section {
  margin-bottom: 4rem;
}

.stats-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.stat-item {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: var(--transition);
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.stat-number {
  font-size: 2rem;
  font-weight: bold;
  color: var(--primary);
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #666;
  font-size: 0.9rem;
}

/* 通用区域样式 */
.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  font-size: 2rem;
  color: var(--secondary);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.section-title i {
  color: var(--accent);
}

.section-subtitle {
  color: #666;
  font-size: 1.1rem;
  max-width: 600px;
  margin: 0 auto;
}

/* 最新宠物 */
.latest-pets-section {
  margin-bottom: 4rem;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding: 0 2rem;
}

.pets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.pet-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--card-shadow);
  transition: var(--transition);
  cursor: pointer;
}

.pet-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.pet-image {
  height: 200px;
  background: linear-gradient(45deg, #64b3f4, #c2e59c);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 4rem;
}

.pet-info {
  padding: 1.5rem;
}

.pet-name {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--secondary);
}

.pet-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.8rem;
  color: #666;
  font-size: 0.9rem;
}

.pet-meta span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.pet-description {
  color: #666;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.section-footer {
  text-align: center;
}

/* 领养流程 */
.process-section {
  background: var(--light);
  padding: 4rem 0;
  margin-bottom: 4rem;
}

.process-steps {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.step-item {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  position: relative;
  box-shadow: var(--card-shadow);
}

.step-number {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 30px;
  background: var(--accent);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.9rem;
}

.step-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  margin: 0 auto 1.5rem;
}

.step-title {
  font-size: 1.2rem;
  color: var(--secondary);
  margin-bottom: 1rem;
}

.step-description {
  color: #666;
  line-height: 1.5;
}

/* 成功故事 */
.success-stories-section {
  margin-bottom: 4rem;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding: 0 2rem;
}

.stories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.story-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: var(--card-shadow);
  transition: var(--transition);
}

.story-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.story-image {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
}

.story-title {
  font-size: 1.2rem;
  color: var(--secondary);
  margin-bottom: 1rem;
}

.story-text {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.story-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  color: #999;
}

.story-author {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .hero-title {
    font-size: 2rem;
    justify-content: center;
  }
  
  .image-placeholder {
    width: 200px;
    height: 200px;
  }
  
  .image-placeholder i {
    font-size: 3rem;
  }
  
  .stats-container {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .process-steps {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stories-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .stats-container,
  .process-steps {
    grid-template-columns: 1fr;
  }
  
  .hero-actions {
    flex-direction: column;
  }
  
  .btn-large {
    width: 100%;
    justify-content: center;
  }
}
</style>
