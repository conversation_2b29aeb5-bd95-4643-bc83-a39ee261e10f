<template>
  <el-dialog
    v-model="dialogVisible"
    title="申请详情"
    width="700px"
    class="application-detail-dialog"
  >
    <div v-if="application" class="dialog-content">
      <!-- 申请状态 -->
      <div class="status-section">
        <div class="status-header">
          <h3>申请状态</h3>
          <el-tag
            :type="getStatusTagType(application.status)"
            size="large"
            effect="dark"
          >
            <i :class="getStatusIcon(application.status)"></i>
            {{ getStatusText(application.status, 'application') }}
          </el-tag>
        </div>
        
        <div v-if="application.status === 'approved'" class="status-message success">
          <i class="fas fa-check-circle"></i>
          <div>
            <p class="message-title">恭喜！您的申请已通过审核</p>
            <p class="message-content">请联系我们完成领养手续：400-123-4567</p>
          </div>
        </div>
        
        <div v-else-if="application.status === 'rejected'" class="status-message error">
          <i class="fas fa-times-circle"></i>
          <div>
            <p class="message-title">很遗憾，您的申请未通过审核</p>
            <p v-if="application.admin_notes" class="message-content">
              原因：{{ application.admin_notes }}
            </p>
          </div>
        </div>
        
        <div v-else class="status-message info">
          <i class="fas fa-clock"></i>
          <div>
            <p class="message-title">您的申请正在审核中</p>
            <p class="message-content">我们会在3个工作日内完成审核，请耐心等待</p>
          </div>
        </div>
      </div>

      <!-- 宠物信息 -->
      <div class="pet-section">
        <h3 class="section-title">
          <i class="fas fa-paw"></i>
          申请宠物
        </h3>
        <div class="pet-info-card">
          <div class="pet-avatar">
            <i :class="getPetIcon(application.pet?.species)"></i>
          </div>
          <div class="pet-details">
            <h4 class="pet-name">{{ application.pet?.name }}</h4>
            <div class="pet-meta">
              <span><i class="fas fa-tag"></i> {{ application.pet?.breed }}</span>
              <span><i class="fas fa-birthday-cake"></i> {{ application.pet?.age }}岁</span>
              <span><i class="fas fa-venus-mars"></i> {{ application.pet?.gender }}</span>
            </div>
            <p class="pet-description">{{ application.pet?.description }}</p>
          </div>
        </div>
      </div>

      <!-- 申请信息 -->
      <div class="application-section">
        <h3 class="section-title">
          <i class="fas fa-file-alt"></i>
          申请信息
        </h3>
        <div class="application-details">
          <div class="detail-grid">
            <div class="detail-item">
              <label>申请理由</label>
              <span>{{ getReasonText(application.reason) }}</span>
            </div>
            <div class="detail-item">
              <label>养宠经验</label>
              <span>{{ getExperienceText(application.experience) }}</span>
            </div>
            <div class="detail-item">
              <label>居住环境</label>
              <span>{{ getLivingSituationText(application.living_situation) }}</span>
            </div>
            <div class="detail-item">
              <label>家庭成员</label>
              <span>{{ application.family_members }}人</span>
            </div>
            <div class="detail-item">
              <label>其他宠物</label>
              <span>{{ application.has_other_pets ? '有' : '无' }}</span>
            </div>
            <div v-if="application.has_other_pets" class="detail-item full-width">
              <label>其他宠物描述</label>
              <span>{{ application.other_pets_description }}</span>
            </div>
            <div class="detail-item">
              <label>每日陪伴时间</label>
              <span>{{ getDailyTimeText(application.daily_time) }}</span>
            </div>
            <div class="detail-item">
              <label>月度预算</label>
              <span>{{ application.monthly_budget }}</span>
            </div>
          </div>
          
          <div class="detail-item full-width">
            <label>详细说明</label>
            <div class="notes-content">{{ application.notes }}</div>
          </div>
        </div>
      </div>

      <!-- 时间信息 -->
      <div class="timeline-section">
        <h3 class="section-title">
          <i class="fas fa-history"></i>
          申请时间线
        </h3>
        <div class="timeline">
          <div class="timeline-item">
            <div class="timeline-dot submitted"></div>
            <div class="timeline-content">
              <h4>申请提交</h4>
              <p>{{ formatDate(application.create_time, 'YYYY-MM-DD HH:mm:ss') }}</p>
            </div>
          </div>
          
          <div v-if="application.status !== 'pending'" class="timeline-item">
            <div class="timeline-dot" :class="application.status"></div>
            <div class="timeline-content">
              <h4>{{ application.status === 'approved' ? '审核通过' : '审核拒绝' }}</h4>
              <p>{{ formatDate(application.update_time, 'YYYY-MM-DD HH:mm:ss') }}</p>
              <p v-if="application.admin_notes" class="admin-notes">
                管理员备注：{{ application.admin_notes }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button
          type="primary"
          @click="viewPet"
        >
          <i class="fas fa-paw"></i>
          查看宠物
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { getPetIcon, getStatusText, formatDate } from '@/utils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  application: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue'])

const router = useRouter()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 获取状态标签类型
const getStatusTagType = (status) => {
  const typeMap = {
    'pending': 'warning',
    'approved': 'success',
    'rejected': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取状态图标
const getStatusIcon = (status) => {
  const iconMap = {
    'pending': 'fas fa-clock',
    'approved': 'fas fa-check-circle',
    'rejected': 'fas fa-times-circle'
  }
  return iconMap[status] || 'fas fa-question-circle'
}

// 获取申请理由文本
const getReasonText = (reason) => {
  const reasonMap = {
    'breed_preference': '喜欢这个品种',
    'companion_for_child': '为孩子寻找伙伴',
    'personal_companion': '个人陪伴需求',
    'experienced_owner': '有养宠经验',
    'rescue_animal': '救助流浪动物',
    'other': '其他原因'
  }
  return reasonMap[reason] || reason
}

// 获取经验文本
const getExperienceText = (experience) => {
  const experienceMap = {
    'none': '没有经验',
    'some': '有一些经验',
    'experienced': '经验丰富'
  }
  return experienceMap[experience] || experience
}

// 获取居住环境文本
const getLivingSituationText = (situation) => {
  const situationMap = {
    'house_with_yard': '独立房屋带院子',
    'house_no_yard': '独立房屋无院子',
    'apartment': '公寓/楼房',
    'dormitory': '宿舍',
    'other': '其他'
  }
  return situationMap[situation] || situation
}

// 获取每日时间文本
const getDailyTimeText = (time) => {
  const timeMap = {
    '1-2': '1-2小时',
    '3-4': '3-4小时',
    '5-6': '5-6小时',
    'full_time': '全天在家'
  }
  return timeMap[time] || time
}

// 查看宠物
const viewPet = () => {
  if (props.application?.pet?.id) {
    router.push(`/pets/${props.application.pet.id}`)
    dialogVisible.value = false
  }
}
</script>

<style scoped>
.application-detail-dialog {
  --el-dialog-border-radius: 12px;
}

.dialog-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0 1rem;
}

.status-section {
  margin-bottom: 2rem;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.status-header h3 {
  margin: 0;
  color: var(--secondary);
}

.status-message {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  align-items: flex-start;
}

.status-message.success {
  background: rgba(39, 174, 96, 0.1);
  border: 1px solid rgba(39, 174, 96, 0.2);
  color: #27ae60;
}

.status-message.error {
  background: rgba(231, 76, 60, 0.1);
  border: 1px solid rgba(231, 76, 60, 0.2);
  color: #e74c3c;
}

.status-message.info {
  background: rgba(52, 152, 219, 0.1);
  border: 1px solid rgba(52, 152, 219, 0.2);
  color: #3498db;
}

.status-message i {
  font-size: 1.2rem;
  margin-top: 0.2rem;
}

.message-title {
  font-weight: 600;
  margin-bottom: 0.3rem;
}

.message-content {
  font-size: 0.9rem;
  opacity: 0.8;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--secondary);
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.section-title i {
  color: var(--accent);
}

.pet-section,
.application-section,
.timeline-section {
  margin-bottom: 2rem;
}

.pet-info-card {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: var(--light);
  border-radius: 8px;
}

.pet-avatar {
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #64b3f4, #c2e59c);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.pet-details {
  flex: 1;
}

.pet-name {
  color: var(--secondary);
  margin-bottom: 0.5rem;
}

.pet-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
  color: #666;
  font-size: 0.9rem;
}

.pet-meta span {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.pet-description {
  color: #666;
  line-height: 1.5;
  font-size: 0.9rem;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 1rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-item label {
  font-weight: 500;
  color: var(--secondary);
  font-size: 0.9rem;
}

.detail-item span {
  color: var(--dark);
}

.notes-content {
  background: var(--light);
  padding: 1rem;
  border-radius: 6px;
  line-height: 1.6;
  white-space: pre-wrap;
}

.timeline {
  position: relative;
  padding-left: 2rem;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 15px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--gray);
}

.timeline-item {
  position: relative;
  margin-bottom: 1.5rem;
}

.timeline-dot {
  position: absolute;
  left: -2rem;
  top: 0.3rem;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 0 0 2px var(--gray);
}

.timeline-dot.submitted {
  background: var(--primary);
  box-shadow: 0 0 0 2px var(--primary);
}

.timeline-dot.approved {
  background: #27ae60;
  box-shadow: 0 0 0 2px #27ae60;
}

.timeline-dot.rejected {
  background: #e74c3c;
  box-shadow: 0 0 0 2px #e74c3c;
}

.timeline-content h4 {
  margin-bottom: 0.3rem;
  color: var(--secondary);
}

.timeline-content p {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.3rem;
}

.admin-notes {
  font-style: italic;
  color: #999;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .status-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .pet-info-card {
    flex-direction: column;
    text-align: center;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
  
  .pet-meta {
    justify-content: center;
    flex-wrap: wrap;
  }
}
</style>
