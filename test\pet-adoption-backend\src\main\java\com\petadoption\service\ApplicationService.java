package com.petadoption.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.petadoption.dto.request.ApplicationCreateRequest;
import com.petadoption.dto.request.ApplicationReviewRequest;
import com.petadoption.dto.response.ApplicationResponse;
import com.petadoption.entity.Application;
import com.petadoption.entity.Pet;
import com.petadoption.entity.User;
import com.petadoption.exception.BusinessException;
import com.petadoption.mapper.ApplicationMapper;
import com.petadoption.mapper.PetMapper;
import com.petadoption.mapper.UserMapper;
import com.petadoption.util.PageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 申请服务
 * 处理领养申请相关的业务逻辑
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApplicationService {
    
    private final ApplicationMapper applicationMapper;
    private final PetMapper petMapper;
    private final UserMapper userMapper;
    private final AuthService authService;
    private final PetService petService;
    
    /**
     * 创建领养申请
     * 
     * @param request 申请请求
     * @return 申请响应
     */
    @Transactional
    public ApplicationResponse createApplication(ApplicationCreateRequest request) {
        log.info("创建领养申请: 宠物ID {}", request.getPetId());
        
        // 获取当前用户
        Long currentUserId = authService.getCurrentUserId();
        
        // 验证宠物是否存在且可领养
        Pet pet = petMapper.selectById(request.getPetId());
        if (pet == null) {
            throw BusinessException.petNotFound();
        }
        
        if (pet.getIsAdopted()) {
            throw new BusinessException("该宠物已被领养");
        }
        
        // 检查用户是否已申请过该宠物
        Long existingCount = applicationMapper.existsByUserIdAndPetId(currentUserId, request.getPetId());
        if (existingCount > 0) {
            throw new BusinessException("您已申请过该宠物，请勿重复申请");
        }
        
        // 创建申请
        Application application = new Application();
        application.setUserId(currentUserId);
        application.setPetId(request.getPetId());
        application.setReason(request.getReason());
        application.setExperience(request.getExperience());
        application.setLivingSituation(request.getLivingSituation());
        application.setFamilyMembers(request.getFamilyMembers());
        application.setHasOtherPets(request.getHasOtherPets());
        application.setOtherPetsDescription(request.getOtherPetsDescription());
        application.setDailyTime(request.getDailyTime());
        application.setMonthlyBudget(request.getMonthlyBudget());
        application.setNotes(request.getNotes());
        application.setContactPhone(request.getContactPhone());
        application.setContactEmail(request.getContactEmail());
        application.setEmergencyContactName(request.getEmergencyContactName());
        application.setEmergencyContactPhone(request.getEmergencyContactPhone());
        application.setAgreeHomeVisit(request.getAgreeHomeVisit());
        application.setAgreeFollowUp(request.getAgreeFollowUp());
        
        applicationMapper.insert(application);
        
        log.info("申请创建成功: ID {}", application.getId());
        
        return convertToResponse(application);
    }
    
    /**
     * 审核申请
     * 
     * @param applicationId 申请ID
     * @param request 审核请求
     * @return 申请响应
     */
    @Transactional
    public ApplicationResponse reviewApplication(Long applicationId, ApplicationReviewRequest request) {
        log.info("审核申请: ID {}, 状态: {}", applicationId, request.getStatus());
        
        // 获取当前管理员
        Long currentUserId = authService.getCurrentUserId();
        
        // 获取申请
        Application application = applicationMapper.selectById(applicationId);
        if (application == null) {
            throw new BusinessException("申请不存在");
        }
        
        if (!application.isPending()) {
            throw new BusinessException("申请已被审核，无法重复审核");
        }
        
        // 更新申请状态
        if ("approved".equals(request.getStatus())) {
            application.approve(currentUserId, request.getAdminNotes());
            
            // 如果通过，更新宠物状态为已领养
            petService.updateAdoptionStatus(application.getPetId(), true, application.getUserId());
        } else if ("rejected".equals(request.getStatus())) {
            application.reject(currentUserId, request.getAdminNotes());
        }
        
        applicationMapper.updateById(application);
        
        log.info("申请审核完成: ID {}, 状态: {}", applicationId, request.getStatus());
        
        return convertToResponse(application);
    }
    
    /**
     * 取消申请
     * 
     * @param applicationId 申请ID
     */
    @Transactional
    public void cancelApplication(Long applicationId) {
        log.info("取消申请: ID {}", applicationId);
        
        // 获取当前用户
        Long currentUserId = authService.getCurrentUserId();
        
        // 获取申请
        Application application = applicationMapper.selectById(applicationId);
        if (application == null) {
            throw new BusinessException("申请不存在");
        }
        
        // 验证权限
        if (!application.getUserId().equals(currentUserId)) {
            throw new BusinessException("无权限取消该申请");
        }
        
        if (!application.isPending()) {
            throw new BusinessException("只能取消待审核的申请");
        }
        
        // 删除申请
        applicationMapper.deleteById(applicationId);
        
        log.info("申请取消成功: ID {}", applicationId);
    }
    
    /**
     * 获取申请详情
     * 
     * @param applicationId 申请ID
     * @return 申请响应
     */
    @Transactional(readOnly = true)
    public ApplicationResponse getApplicationById(Long applicationId) {
        Application application = applicationMapper.selectById(applicationId);
        if (application == null) {
            throw new BusinessException("申请不存在");
        }
        
        return convertToResponse(application);
    }
    
    /**
     * 获取用户申请列表
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 页面大小
     * @param status 状态筛选
     * @return 分页响应
     */
    @Transactional(readOnly = true)
    public PageUtil.PageResponse<ApplicationResponse> getUserApplications(
            Long userId, Integer page, Integer size, String status) {
        
        Page<Application> pageObj = new Page<>(page != null ? page : 1, size != null ? size : 10);
        
        QueryWrapper<Application> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        
        if (status != null && !status.trim().isEmpty()) {
            queryWrapper.eq("status", status);
        }
        
        queryWrapper.orderByDesc("create_time");
        
        IPage<Application> applicationPage = applicationMapper.selectPage(pageObj, queryWrapper);
        
        List<ApplicationResponse> responses = applicationPage.getRecords().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
        
        PageUtil.PageResponse<ApplicationResponse> response = new PageUtil.PageResponse<>();
        response.setData(responses);
        response.setTotal(applicationPage.getTotal());
        response.setPage((int) applicationPage.getCurrent());
        response.setSize((int) applicationPage.getSize());
        response.setTotalPages((int) applicationPage.getPages());
        response.setHasNext(applicationPage.getCurrent() < applicationPage.getPages());
        response.setHasPrevious(applicationPage.getCurrent() > 1);
        
        return response;
    }
    
    /**
     * 获取所有申请列表
     * 
     * @param page 页码
     * @param size 页面大小
     * @param status 状态筛选
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 分页响应
     */
    @Transactional(readOnly = true)
    public PageUtil.PageResponse<ApplicationResponse> getAllApplications(
            Integer page, Integer size, String status, LocalDateTime startDate, LocalDateTime endDate) {
        
        Page<Application> pageObj = new Page<>(page != null ? page : 1, size != null ? size : 10);
        
        QueryWrapper<Application> queryWrapper = new QueryWrapper<>();
        
        if (status != null && !status.trim().isEmpty()) {
            queryWrapper.eq("status", status);
        }
        
        if (startDate != null) {
            queryWrapper.ge("create_time", startDate);
        }
        
        if (endDate != null) {
            queryWrapper.le("create_time", endDate);
        }
        
        queryWrapper.orderByDesc("create_time");
        
        IPage<Application> applicationPage = applicationMapper.selectPage(pageObj, queryWrapper);
        
        List<ApplicationResponse> responses = applicationPage.getRecords().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
        
        PageUtil.PageResponse<ApplicationResponse> response = new PageUtil.PageResponse<>();
        response.setData(responses);
        response.setTotal(applicationPage.getTotal());
        response.setPage((int) applicationPage.getCurrent());
        response.setSize((int) applicationPage.getSize());
        response.setTotalPages((int) applicationPage.getPages());
        response.setHasNext(applicationPage.getCurrent() < applicationPage.getPages());
        response.setHasPrevious(applicationPage.getCurrent() > 1);
        
        return response;
    }
    
    /**
     * 转换为响应DTO
     * 
     * @param application 申请实体
     * @return 申请响应
     */
    private ApplicationResponse convertToResponse(Application application) {
        ApplicationResponse response = ApplicationResponse.builder()
                .id(application.getId())
                .userId(application.getUserId())
                .petId(application.getPetId())
                .status(application.getStatus())
                .reason(application.getReason())
                .experience(application.getExperience())
                .livingSituation(application.getLivingSituation())
                .familyMembers(application.getFamilyMembers())
                .hasOtherPets(application.getHasOtherPets())
                .otherPetsDescription(application.getOtherPetsDescription())
                .dailyTime(application.getDailyTime())
                .monthlyBudget(application.getMonthlyBudget())
                .notes(application.getNotes())
                .adminNotes(application.getAdminNotes())
                .reviewerId(application.getReviewerId())
                .reviewTime(application.getReviewTime())
                .contactPhone(application.getContactPhone())
                .contactEmail(application.getContactEmail())
                .emergencyContactName(application.getEmergencyContactName())
                .emergencyContactPhone(application.getEmergencyContactPhone())
                .agreeHomeVisit(application.getAgreeHomeVisit())
                .agreeFollowUp(application.getAgreeFollowUp())
                .createTime(application.getCreateTime())
                .updateTime(application.getUpdateTime())
                .build();
        
        // 加载关联的用户信息
        User user = userMapper.selectById(application.getUserId());
        if (user != null) {
            response.setUser(ApplicationResponse.UserInfo.builder()
                    .id(user.getId())
                    .username(user.getUsername())
                    .email(user.getEmail())
                    .phone(user.getPhone())
                    .realName(user.getRealName())
                    .build());
        }
        
        // 加载关联的宠物信息
        Pet pet = petMapper.selectById(application.getPetId());
        if (pet != null) {
            response.setPet(ApplicationResponse.PetInfo.builder()
                    .id(pet.getId())
                    .name(pet.getName())
                    .species(pet.getSpecies())
                    .breed(pet.getBreed())
                    .age(pet.getAge())
                    .gender(pet.getGender())
                    .imageUrl(pet.getImageUrl())
                    .isAdopted(pet.getIsAdopted())
                    .build());
        }
        
        return response;
    }
}
