package com.petadoption.security;

import com.petadoption.service.UserDetailsServiceImpl;
import com.petadoption.util.JwtUtil;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.UnsupportedJwtException;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * JWT认证过滤器
 * 从请求头中提取JWT Token并验证，设置Spring Security上下文
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    
    private final JwtUtil jwtUtil;
    private final UserDetailsServiceImpl userDetailsService;
    
    @Override
    protected void doFilterInternal(
            @NonNull HttpServletRequest request,
            @NonNull HttpServletResponse response,
            @NonNull FilterChain filterChain
    ) throws ServletException, IOException {
        
        // 获取请求路径
        String requestPath = request.getRequestURI();
        
        // 跳过不需要认证的路径
        if (shouldSkipAuthentication(requestPath, request)) {
            filterChain.doFilter(request, response);
            return;
        }
        
        // 从请求头中获取Authorization
        final String authHeader = request.getHeader("Authorization");
        
        // 检查Authorization头是否存在且格式正确
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            filterChain.doFilter(request, response);
            return;
        }
        
        try {
            // 提取JWT Token
            final String jwt = jwtUtil.extractTokenFromHeader(authHeader);
            
            if (jwt != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                // 从Token中获取用户名
                final String username = jwtUtil.getUsernameFromToken(jwt);
                
                if (username != null) {
                    // 加载用户详情
                    UserDetails userDetails = userDetailsService.loadUserByUsername(username);
                    
                    // 验证Token
                    if (jwtUtil.validateToken(jwt, userDetails.getUsername())) {
                        // 创建认证对象
                        UsernamePasswordAuthenticationToken authToken = 
                            new UsernamePasswordAuthenticationToken(
                                userDetails,
                                null,
                                userDetails.getAuthorities()
                            );
                        
                        // 设置认证详情
                        authToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                        
                        // 设置到Security上下文
                        SecurityContextHolder.getContext().setAuthentication(authToken);
                        
                        log.debug("用户 {} 认证成功", username);
                    } else {
                        log.warn("JWT Token验证失败: {}", username);
                    }
                }
            }
        } catch (ExpiredJwtException e) {
            log.warn("JWT Token已过期: {}", e.getMessage());
            setErrorResponse(response, "Token已过期，请重新登录");
            return;
        } catch (UnsupportedJwtException e) {
            log.warn("不支持的JWT Token: {}", e.getMessage());
            setErrorResponse(response, "Token格式不支持");
            return;
        } catch (MalformedJwtException e) {
            log.warn("JWT Token格式错误: {}", e.getMessage());
            setErrorResponse(response, "Token格式错误");
            return;
        } catch (SecurityException e) {
            log.warn("JWT Token签名验证失败: {}", e.getMessage());
            setErrorResponse(response, "Token签名验证失败");
            return;
        } catch (IllegalArgumentException e) {
            log.warn("JWT Token参数错误: {}", e.getMessage());
            setErrorResponse(response, "Token参数错误");
            return;
        } catch (Exception e) {
            log.error("JWT认证过程中发生异常: ", e);
            setErrorResponse(response, "认证失败");
            return;
        }
        
        // 继续过滤器链
        filterChain.doFilter(request, response);
    }
    
    /**
     * 判断是否应该跳过认证
     */
    private boolean shouldSkipAuthentication(String requestPath, HttpServletRequest request) {
        // 公开API路径
        String[] publicPaths = {
            "/api/auth/login",
            "/api/auth/register",
            "/api/auth/check",
            "/uploads/",
            "/static/",
            "/actuator/health",
            "/actuator/info",
            "/swagger-ui/",
            "/v3/api-docs/",
            "/favicon.ico"
        };
        
        for (String path : publicPaths) {
            if (requestPath.startsWith(path)) {
                return true;
            }
        }
        
        // GET请求的宠物相关API
        return requestPath.startsWith("/api/pets") && "GET".equals(request.getMethod());
    }
    

    
    /**
     * 设置错误响应
     */
    private void setErrorResponse(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        
        String jsonResponse = String.format(
            "{\"code\": 401, \"message\": \"%s\", \"timestamp\": \"%s\"}",
            message,
            java.time.LocalDateTime.now().toString()
        );
        
        response.getWriter().write(jsonResponse);
    }
}
