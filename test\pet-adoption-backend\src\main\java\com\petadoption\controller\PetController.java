package com.petadoption.controller;

import com.petadoption.common.Result;
import com.petadoption.dto.request.PetCreateRequest;
import com.petadoption.dto.request.PetUpdateRequest;
import com.petadoption.dto.response.PetResponse;
import com.petadoption.service.PetService;
import com.petadoption.util.PageUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 宠物控制器
 * 处理宠物相关的HTTP请求
 * 与前端宠物API保持一致
 * 
 * <AUTHOR> Team
 */
@Slf4j
@RestController
@RequestMapping("/api/pets")
@RequiredArgsConstructor
public class PetController {
    
    private final PetService petService;
    
    /**
     * 创建宠物（管理员）
     * 
     * @param request 创建请求
     * @return 宠物响应
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public Result<PetResponse> createPet(@Valid @RequestBody PetCreateRequest request) {
        log.info("创建宠物请求: {}", request.getName());
        
        PetResponse response = petService.createPet(request);
        
        return Result.success("宠物创建成功", response);
    }
    
    /**
     * 更新宠物信息（管理员）
     * 
     * @param petId 宠物ID
     * @param request 更新请求
     * @return 宠物响应
     */
    @PutMapping("/{petId}")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<PetResponse> updatePet(
            @PathVariable Long petId,
            @Valid @RequestBody PetUpdateRequest request
    ) {
        log.info("更新宠物请求: ID {}", petId);
        
        PetResponse response = petService.updatePet(petId, request);
        
        return Result.success("宠物信息更新成功", response);
    }
    
    /**
     * 删除宠物（管理员）
     * 
     * @param petId 宠物ID
     * @return 响应结果
     */
    @DeleteMapping("/{petId}")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> deletePet(@PathVariable Long petId) {
        log.info("删除宠物请求: ID {}", petId);
        
        petService.deletePet(petId);
        
        return Result.<Void>success();
    }
    
    /**
     * 获取宠物详情
     * 
     * @param petId 宠物ID
     * @return 宠物响应
     */
    @GetMapping("/{petId}")
    public Result<PetResponse> getPetById(@PathVariable Long petId) {
        PetResponse response = petService.getPetById(petId);
        
        return Result.success("获取宠物详情成功", response);
    }
    
    /**
     * 分页查询宠物列表
     * 
     * @param page 页码
     * @param size 页面大小
     * @param species 种类筛选
     * @param status 状态筛选（available/adopted/all）
     * @param keyword 关键词搜索
     * @param sortBy 排序字段
     * @param sortDir 排序方向
     * @return 分页响应
     */
    @GetMapping
    public Result<PageUtil.PageResponse<PetResponse>> getPets(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "12") Integer size,
            @RequestParam(required = false) String species,
            @RequestParam(defaultValue = "available") String status,
            @RequestParam(required = false) String keyword,
            @RequestParam(defaultValue = "createTime") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir
    ) {
        // 转换状态参数
        Boolean isAdopted = null;
        if ("available".equals(status)) {
            isAdopted = false;
        } else if ("adopted".equals(status)) {
            isAdopted = true;
        }
        // "all"状态时isAdopted保持null，查询所有状态
        
        PageUtil.PageResponse<PetResponse> response = petService.getPets(
                page, size, species, isAdopted, keyword, sortBy, sortDir);
        
        return Result.success("获取宠物列表成功", response);
    }
    
    /**
     * 获取最新宠物列表
     * 
     * @param limit 数量限制
     * @return 宠物列表
     */
    @GetMapping("/latest")
    public Result<List<PetResponse>> getLatestPets(
            @RequestParam(defaultValue = "6") Integer limit
    ) {
        List<PetResponse> response = petService.getLatestPets(limit);
        
        return Result.success("获取最新宠物列表成功", response);
    }
    
    /**
     * 获取推荐宠物列表
     * 
     * @param petId 当前宠物ID
     * @param limit 数量限制
     * @return 推荐宠物列表
     */
    @GetMapping("/{petId}/recommended")
    public Result<List<PetResponse>> getRecommendedPets(
            @PathVariable Long petId,
            @RequestParam(defaultValue = "4") Integer limit
    ) {
        List<PetResponse> response = petService.getRecommendedPets(petId, limit);
        
        return Result.success("获取推荐宠物列表成功", response);
    }
    
    /**
     * 更新宠物领养状态（管理员）
     * 
     * @param petId 宠物ID
     * @param isAdopted 是否已领养
     * @param adopterId 领养者ID
     * @return 响应结果
     */
    @PatchMapping("/{petId}/adoption-status")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> updateAdoptionStatus(
            @PathVariable Long petId,
            @RequestParam Boolean isAdopted,
            @RequestParam(required = false) Long adopterId
    ) {
        log.info("更新宠物领养状态: ID {}, 已领养: {}, 领养者: {}", petId, isAdopted, adopterId);
        
        petService.updateAdoptionStatus(petId, isAdopted, adopterId);
        
        return Result.<Void>success();
    }
}
