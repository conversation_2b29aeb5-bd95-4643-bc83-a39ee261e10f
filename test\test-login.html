<!DOCTYPE html>
<html>
<head>
    <title>登录测试</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>登录测试</h1>
    <div>
        <label>用户名: <input type="text" id="username" value="admin"></label><br><br>
        <label>密码: <input type="password" id="password" value="admin123"></label><br><br>
        <button onclick="testLogin()">测试登录</button>
    </div>
    <div id="result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc;"></div>

    <script>
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            try {
                resultDiv.innerHTML = '正在测试登录...';
                
                const response = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <h3>响应状态: ${response.status}</h3>
                    <h3>响应数据:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
                if (response.ok) {
                    resultDiv.style.backgroundColor = '#d4edda';
                    resultDiv.style.color = '#155724';
                } else {
                    resultDiv.style.backgroundColor = '#f8d7da';
                    resultDiv.style.color = '#721c24';
                }
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <h3>错误:</h3>
                    <pre>${error.message}</pre>
                `;
                resultDiv.style.backgroundColor = '#f8d7da';
                resultDiv.style.color = '#721c24';
            }
        }
    </script>
</body>
</html>
