package com.petadoption.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.petadoption.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户Mapper接口
 * 
 * <AUTHOR> Team
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据用户名查找用户
     */
    @Select("SELECT * FROM users WHERE username = #{username}")
    User findByUsername(@Param("username") String username);

    /**
     * 根据邮箱查找用户
     */
    @Select("SELECT * FROM users WHERE email = #{email}")
    User findByEmail(@Param("email") String email);

    /**
     * 根据手机号查找用户
     */
    @Select("SELECT * FROM users WHERE phone = #{phone}")
    User findByPhone(@Param("phone") String phone);

    /**
     * 检查用户名是否存在
     */
    @Select("SELECT COUNT(*) FROM users WHERE username = #{username}")
    Long existsByUsername(@Param("username") String username);

    /**
     * 检查邮箱是否存在
     */
    @Select("SELECT COUNT(*) FROM users WHERE email = #{email}")
    Long existsByEmail(@Param("email") String email);

    /**
     * 检查手机号是否存在
     */
    @Select("SELECT COUNT(*) FROM users WHERE phone = #{phone}")
    Long existsByPhone(@Param("phone") String phone);

    /**
     * 根据角色分页查询用户
     */
    @Select("SELECT * FROM users WHERE role = #{role} ORDER BY create_time DESC")
    IPage<User> findByRole(Page<User> page, @Param("role") String role);

    /**
     * 统计用户总数
     */
    @Select("SELECT COUNT(*) FROM users")
    Long countAllUsers();

    /**
     * 统计管理员数量
     */
    @Select("SELECT COUNT(*) FROM users WHERE role = 'ADMIN'")
    Long countAdmins();

    /**
     * 统计普通用户数量
     */
    @Select("SELECT COUNT(*) FROM users WHERE role = 'USER'")
    Long countRegularUsers();

    /**
     * 根据角色统计用户数量
     */
    @Select("SELECT COUNT(*) FROM users WHERE role = #{role}")
    Long countByRole(@Param("role") String role);

    /**
     * 根据创建时间范围统计用户数量
     */
    @Select("SELECT COUNT(*) FROM users WHERE create_time BETWEEN #{startTime} AND #{endTime}")
    Long countByCreateTimeBetween(@Param("startTime") java.time.LocalDateTime startTime,
                                  @Param("endTime") java.time.LocalDateTime endTime);

    /**
     * 根据用户名或邮箱查找用户
     */
    @Select("SELECT * FROM users WHERE username = #{usernameOrEmail} OR email = #{usernameOrEmail}")
    User findByUsernameOrEmail(@Param("usernameOrEmail") String usernameOrEmail, @Param("usernameOrEmail") String usernameOrEmail2);
}
