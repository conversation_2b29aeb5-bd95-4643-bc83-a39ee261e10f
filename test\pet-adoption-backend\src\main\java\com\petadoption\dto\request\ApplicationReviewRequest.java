package com.petadoption.dto.request;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 申请审核请求DTO
 * 与前端审核表单数据结构保持一致
 * 
 * <AUTHOR> Team
 */
@Data
public class ApplicationReviewRequest {
    
    /**
     * 审核状态
     * approved: 通过
     * rejected: 拒绝
     */
    @NotBlank(message = "审核状态不能为空")
    @Pattern(regexp = "^(approved|rejected)$", message = "审核状态只能是approved或rejected")
    private String status;
    
    /**
     * 管理员备注
     */
    @Size(max = 500, message = "管理员备注长度不能超过500个字符")
    private String adminNotes;
}
