package com.petadoption.controller;

import com.petadoption.common.Result;
import com.petadoption.dto.response.StatisticsResponse;
import com.petadoption.service.StatisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 管理员控制器
 * 处理管理员相关的HTTP请求
 * 与前端管理员API保持一致
 * 
 * <AUTHOR> Team
 */
@Slf4j
@RestController
@RequestMapping("/api/admin")
@PreAuthorize("hasRole('ADMIN')")
@RequiredArgsConstructor
public class AdminController {
    
    private final StatisticsService statisticsService;
    
    /**
     * 获取系统统计数据
     * 
     * @return 统计响应
     */
    @GetMapping("/statistics")
    public Result<StatisticsResponse> getStatistics() {
        log.info("获取系统统计数据");
        
        StatisticsResponse response = statisticsService.getSystemStatistics();
        
        return Result.success("获取统计数据成功", response);
    }
    
    /**
     * 获取最近7天趋势数据
     * 
     * @return 趋势数据
     */
    @GetMapping("/trends/weekly")
    public Result<Map<String, Object>> getWeeklyTrends() {
        log.info("获取最近7天趋势数据");
        
        Map<String, Object> response = statisticsService.getWeeklyTrends();
        
        return Result.success("获取周趋势数据成功", response);
    }
    
    /**
     * 获取最近12个月趋势数据
     * 
     * @return 月度趋势数据
     */
    @GetMapping("/trends/monthly")
    public Result<Map<String, Object>> getMonthlyTrends() {
        log.info("获取最近12个月趋势数据");
        
        Map<String, Object> response = statisticsService.getMonthlyTrends();
        
        return Result.success("获取月度趋势数据成功", response);
    }
}
