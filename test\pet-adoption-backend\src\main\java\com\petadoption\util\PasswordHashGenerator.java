package com.petadoption.util;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 * Password Hash Generator Utility
 * Used to generate BCrypt password hashes
 *
 * <AUTHOR> Team
 */
public class PasswordHashGenerator {
    
    public static void main(String[] args) {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        
        // Generate hash for admin123
        String password = "admin123";
        String hash = encoder.encode(password);

        System.out.println("=================================");
        System.out.println("Password Hash Generation Complete");
        System.out.println("=================================");
        System.out.println("Original Password: " + password);
        System.out.println("BCrypt Hash: " + hash);
        System.out.println("=================================");

        // Verify the hash is correct
        boolean matches = encoder.matches(password, hash);
        System.out.println("Verification Result: " + (matches ? "✓ Correct" : "✗ Error"));
        System.out.println("=================================");

        // Test existing hash
        String existingHash = "$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYjKUiFjFO2/8G";
        boolean existingMatches = encoder.matches(password, existingHash);
        System.out.println("Existing Hash Verification: " + (existingMatches ? "✓ Correct" : "✗ Error"));
        System.out.println("Existing Hash: " + existingHash);
        System.out.println("=================================");
    }
}
