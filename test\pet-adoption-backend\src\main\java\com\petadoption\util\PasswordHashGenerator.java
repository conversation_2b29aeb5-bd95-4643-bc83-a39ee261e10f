package com.petadoption.util;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 *Password Hash Generator 实用程序
 * 用于生成 BCrypt 密码哈希
 *
 * <AUTHOR> Team
 */
public class PasswordHashGenerator {
    
    public static void main(String[] args) {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        
        // Generate hash for user01
        String userPassword = "user01";
        String userHash = encoder.encode(userPassword);

        System.out.println("=================================");
        System.out.println("user1 的密码哈希生成");
        System.out.println("=================================");
        System.out.println("New Password: " + userPassword);
        System.out.println("New BCrypt Hash: " + userHash);
        System.out.println("=================================");

        // Verify the hash is correct
        boolean userMatches = encoder.matches(userPassword, userHash);
        System.out.println("验证结果: " + (userMatches ? "✓ Correct" : "✗ Error"));
        System.out.println("=================================");

        // Also generate admin123 hash for reference
        String adminPassword = "admin123";
        String adminHash = encoder.encode(adminPassword);
        System.out.println("Admin Password: " + adminPassword);
        System.out.println("Admin BCrypt Hash: " + adminHash);
        System.out.println("=================================");
    }
}
