<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" th:replace="~{layout/base :: html}">
<head>
    <title>宠物列表 - 宠物领养系统</title>
</head>
<body>
    <th:block th:fragment="content">
        <div class="container py-4">
            <!-- 页面标题 -->
            <div class="row mb-4">
                <div class="col">
                    <h1 class="fw-bold">
                        <i class="fas fa-paw me-2 text-primary"></i>宠物列表
                    </h1>
                    <p class="text-muted">找到您心仪的小伙伴</p>
                </div>
            </div>

            <!-- 搜索和筛选 -->
            <div class="row mb-4">
                <div class="col">
                    <div class="card">
                        <div class="card-body">
                            <form method="get" action="/web/pets">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <label class="form-label">关键词搜索</label>
                                        <input type="text" class="form-control" name="keyword" 
                                               th:value="${param.keyword}" placeholder="宠物名称、品种...">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">种类</label>
                                        <select class="form-select" name="species">
                                            <option value="">全部</option>
                                            <option value="狗" th:selected="${param.species == '狗'}">狗</option>
                                            <option value="猫" th:selected="${param.species == '猫'}">猫</option>
                                            <option value="兔" th:selected="${param.species == '兔'}">兔</option>
                                            <option value="鸟" th:selected="${param.species == '鸟'}">鸟</option>
                                            <option value="其他" th:selected="${param.species == '其他'}">其他</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">性别</label>
                                        <select class="form-select" name="gender">
                                            <option value="">全部</option>
                                            <option value="公" th:selected="${param.gender == '公'}">公</option>
                                            <option value="母" th:selected="${param.gender == '母'}">母</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">年龄范围</label>
                                        <select class="form-select" name="ageRange">
                                            <option value="">全部</option>
                                            <option value="0-1" th:selected="${param.ageRange == '0-1'}">0-1岁</option>
                                            <option value="1-3" th:selected="${param.ageRange == '1-3'}">1-3岁</option>
                                            <option value="3-7" th:selected="${param.ageRange == '3-7'}">3-7岁</option>
                                            <option value="7-15" th:selected="${param.ageRange == '7-15'}">7岁以上</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">状态</label>
                                        <select class="form-select" name="status">
                                            <option value="">全部</option>
                                            <option value="available" th:selected="${param.status == 'available'}">可领养</option>
                                            <option value="adopted" th:selected="${param.status == 'adopted'}">已领养</option>
                                        </select>
                                    </div>
                                    <div class="col-md-1">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="submit" class="btn btn-primary d-block">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 宠物列表 -->
            <div class="row" th:if="${pets != null and pets.records != null and !pets.records.isEmpty()}">
                <div class="col-md-4 mb-4" th:each="pet : ${pets.records}">
                    <div class="card pet-card h-100">
                        <div class="position-relative">
                            <img th:src="${pet.imageUrl} ?: '/images/default-pet.jpg'" 
                                 class="card-img-top pet-image" 
                                 th:alt="${pet.name}">
                            <span class="badge bg-success status-badge" th:if="${!pet.isAdopted}">
                                可领养
                            </span>
                            <span class="badge bg-secondary status-badge" th:if="${pet.isAdopted}">
                                已领养
                            </span>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title" th:text="${pet.name}">宠物名称</h5>
                            <p class="text-muted mb-2">
                                <i class="fas fa-tag me-1"></i>
                                <span th:text="${pet.species}">种类</span> · 
                                <span th:text="${pet.breed}">品种</span>
                            </p>
                            <p class="text-muted mb-2">
                                <i class="fas fa-birthday-cake me-1"></i>
                                <span th:text="${pet.age} + '岁'">年龄</span> · 
                                <i class="fas fa-venus-mars ms-2 me-1"></i>
                                <span th:text="${pet.gender}">性别</span>
                            </p>
                            <p class="text-muted mb-2" th:if="${pet.weight != null}">
                                <i class="fas fa-weight me-1"></i>
                                <span th:text="${pet.weight} + 'kg'">体重</span>
                            </p>
                            <p class="card-text" th:text="${#strings.abbreviate(pet.description, 80)}">
                                宠物描述...
                            </p>
                        </div>
                        <div class="card-footer bg-transparent">
                            <a th:href="@{/web/pets/{id}(id=${pet.id})}" 
                               class="btn btn-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>查看详情
                            </a>
                            <a th:href="@{/web/applications/create(petId=${pet.id})}" 
                               class="btn btn-outline-success btn-sm ms-2"
                               th:if="${!pet.isAdopted}">
                                <i class="fas fa-heart me-1"></i>申请领养
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 空状态 -->
            <div class="text-center py-5" th:if="${pets == null or pets.records == null or pets.records.isEmpty()}">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">没有找到符合条件的宠物</h4>
                <p class="text-muted">请尝试调整搜索条件</p>
                <a href="/web/pets" class="btn btn-primary">
                    <i class="fas fa-refresh me-1"></i>查看所有宠物
                </a>
            </div>

            <!-- 分页 -->
            <div class="row mt-4" th:if="${pets != null and pets.pages > 1}">
                <div class="col">
                    <nav aria-label="宠物列表分页">
                        <ul class="pagination justify-content-center">
                            <!-- 上一页 -->
                            <li class="page-item" th:classappend="${pets.current <= 1} ? 'disabled'">
                                <a class="page-link" 
                                   th:href="@{/web/pets(page=${pets.current - 1}, keyword=${param.keyword}, species=${param.species}, gender=${param.gender}, ageRange=${param.ageRange}, status=${param.status})}">
                                    上一页
                                </a>
                            </li>
                            
                            <!-- 页码 -->
                            <li class="page-item" 
                                th:each="pageNum : ${#numbers.sequence(1, pets.pages)}"
                                th:if="${pageNum >= pets.current - 2 and pageNum <= pets.current + 2}"
                                th:classappend="${pageNum == pets.current} ? 'active'">
                                <a class="page-link" 
                                   th:href="@{/web/pets(page=${pageNum}, keyword=${param.keyword}, species=${param.species}, gender=${param.gender}, ageRange=${param.ageRange}, status=${param.status})}"
                                   th:text="${pageNum}">1</a>
                            </li>
                            
                            <!-- 下一页 -->
                            <li class="page-item" th:classappend="${pets.current >= pets.pages} ? 'disabled'">
                                <a class="page-link" 
                                   th:href="@{/web/pets(page=${pets.current + 1}, keyword=${param.keyword}, species=${param.species}, gender=${param.gender}, ageRange=${param.ageRange}, status=${param.status})}">
                                    下一页
                                </a>
                            </li>
                        </ul>
                    </nav>
                    
                    <div class="text-center text-muted">
                        <small>
                            第 <span th:text="${pets.current}">1</span> 页，
                            共 <span th:text="${pets.pages}">1</span> 页，
                            总计 <span th:text="${pets.total}">0</span> 只宠物
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </th:block>
</body>
</html>
