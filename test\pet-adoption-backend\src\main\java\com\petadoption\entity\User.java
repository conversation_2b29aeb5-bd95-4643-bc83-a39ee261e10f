package com.petadoption.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.*;
import java.time.LocalDateTime;

/**
 * 用户实体类
 * 对应数据库中的users表
 * 
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("users")
public class User extends BaseEntity {
    
    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 20, message = "用户名长度必须在3-20个字符之间")
    private String username;
    
    /**
     * 密码（加密）
     */
    @NotBlank(message = "密码不能为空")
    private String password;
    
    /**
     * 邮箱
     */
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;
    
    /**
     * 手机号
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    /**
     * 角色：user-普通用户，admin-管理员
     */
    @NotBlank(message = "角色不能为空")
    private String role = "user";
    
    /**
     * 是否启用
     */
    private Boolean enabled = true;
    
    /**
     * 是否锁定
     */
    private Boolean locked = false;
    
    /**
     * 头像URL
     */
    private String avatarUrl;
    
    /**
     * 真实姓名
     */
    @Size(max = 20, message = "真实姓名长度不能超过20个字符")
    private String realName;
    
    /**
     * 身份证号
     */
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", 
             message = "身份证号格式不正确")
    private String idCard;
    
    /**
     * 地址
     */
    @Size(max = 200, message = "地址长度不能超过200个字符")
    private String address;
    
    /**
     * 个人简介
     */
    @Size(max = 500, message = "个人简介长度不能超过500个字符")
    private String bio;
    
    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;
    
    /**
     * 最后登录IP
     */
    private String lastLoginIp;
    
    /**
     * 构造函数
     */
    public User() {
        super();
    }
    
    public User(String username, String password, String email) {
        this();
        this.username = username;
        this.password = password;
        this.email = email;
    }
    
    /**
     * 是否为管理员
     */
    public boolean isAdmin() {
        return "admin".equals(this.role);
    }
    
    /**
     * 是否为普通用户
     */
    public boolean isUser() {
        return "user".equals(this.role);
    }
    
    /**
     * 账户是否可用
     */
    public boolean isAccountNonLocked() {
        return !this.locked;
    }
    
    /**
     * 账户是否启用
     */
    public boolean isEnabled() {
        return this.enabled;
    }
    
    /**
     * 更新最后登录信息
     */
    public void updateLastLogin(String ip) {
        this.lastLoginTime = LocalDateTime.now();
        this.lastLoginIp = ip;
    }
}
