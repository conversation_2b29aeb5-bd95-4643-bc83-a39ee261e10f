<template>
  <div class="pet-manage-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <i class="fas fa-paw"></i>
          宠物管理
        </h1>
        <p class="page-subtitle">管理所有宠物信息和领养状态</p>
      </div>
      <div class="header-actions">
        <el-button
          type="primary"
          size="large"
          @click="$router.push('/manage/add')"
        >
          <i class="fas fa-plus"></i>
          添加宠物
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon total">
            <i class="fas fa-paw"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-label">总宠物数</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon available">
            <i class="fas fa-heart"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.available }}</div>
            <div class="stat-label">待领养</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon adopted">
            <i class="fas fa-home"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.adopted }}</div>
            <div class="stat-label">已领养</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon applications">
            <i class="fas fa-file-alt"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.applications }}</div>
            <div class="stat-label">待审核申请</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filter-section">
      <el-card class="filter-card">
        <div class="filter-content">
          <div class="search-box">
            <el-input
              v-model="searchQuery"
              placeholder="搜索宠物名称或品种..."
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <i class="fas fa-search"></i>
              </template>
            </el-input>
          </div>

          <div class="filters">
            <el-select
              v-model="filters.species"
              placeholder="选择种类"
              clearable
              @change="handleFilterChange"
            >
              <el-option label="全部种类" value="" />
              <el-option label="狗" value="狗" />
              <el-option label="猫" value="猫" />
              <el-option label="兔" value="兔" />
              <el-option label="鸟" value="鸟" />
              <el-option label="其他" value="其他" />
            </el-select>

            <el-select
              v-model="filters.status"
              placeholder="选择状态"
              @change="handleFilterChange"
            >
              <el-option label="全部状态" value="all" />
              <el-option label="待领养" value="available" />
              <el-option label="已领养" value="adopted" />
            </el-select>

            <el-button @click="resetFilters">
              <i class="fas fa-undo"></i>
              重置
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 宠物表格 -->
    <div class="table-section">
      <el-card class="table-card">
        <el-table
          :data="paginatedPets"
          v-loading="petsStore.loading"
          stripe
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          
          <el-table-column label="宠物信息" min-width="200">
            <template #default="{ row }">
              <div class="pet-info-cell">
                <div class="pet-avatar-small">
                  <i :class="getPetIcon(row.species)"></i>
                </div>
                <div class="pet-basic">
                  <div class="pet-name">{{ row.name }}</div>
                  <div class="pet-breed">{{ row.breed }}</div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="species" label="种类" width="80" />
          
          <el-table-column prop="age" label="年龄" width="80">
            <template #default="{ row }">
              {{ row.age }}岁
            </template>
          </el-table-column>

          <el-table-column prop="gender" label="性别" width="80" />

          <el-table-column prop="health_status" label="健康状况" width="120" />

          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag
                :type="row.is_adopted ? 'danger' : 'success'"
                size="small"
              >
                {{ getStatusText(row.is_adopted, 'pet') }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="入站时间" width="120">
            <template #default="{ row }">
              {{ formatDate(row.create_time) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button
                  type="primary"
                  size="small"
                  @click="viewPet(row.id)"
                >
                  <i class="fas fa-eye"></i>
                  查看
                </el-button>
                <el-button
                  type="warning"
                  size="small"
                  @click="editPet(row.id)"
                >
                  <i class="fas fa-edit"></i>
                  编辑
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="deletePet(row)"
                >
                  <i class="fas fa-trash"></i>
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 批量操作 -->
        <div v-if="selectedPets.length > 0" class="batch-actions">
          <div class="batch-info">
            已选择 {{ selectedPets.length }} 只宠物
          </div>
          <div class="batch-buttons">
            <el-button
              type="success"
              @click="batchUpdateStatus(false)"
            >
              <i class="fas fa-heart"></i>
              标记为待领养
            </el-button>
            <el-button
              type="warning"
              @click="batchUpdateStatus(true)"
            >
              <i class="fas fa-home"></i>
              标记为已领养
            </el-button>
            <el-button
              type="danger"
              @click="batchDelete"
            >
              <i class="fas fa-trash"></i>
              批量删除
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="filteredPets.length"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { usePetsStore } from '@/stores/pets'
import { getPetIcon, getStatusText, formatDate, debounce } from '@/utils'

const router = useRouter()
const petsStore = usePetsStore()

// 搜索和筛选
const searchQuery = ref('')
const filters = ref({
  species: '',
  status: 'all'
})

// 分页
const currentPage = ref(1)
const pageSize = ref(20)

// 选中的宠物
const selectedPets = ref([])

// 统计数据
const stats = computed(() => {
  const total = petsStore.pets.length
  const available = petsStore.availablePets.length
  const adopted = petsStore.adoptedPets.length
  // 这里应该从API获取待审核申请数量
  const applications = 12 // 模拟数据

  return { total, available, adopted, applications }
})

// 筛选后的宠物
const filteredPets = computed(() => {
  let result = petsStore.pets

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(pet =>
      pet.name.toLowerCase().includes(query) ||
      pet.breed.toLowerCase().includes(query)
    )
  }

  // 种类筛选
  if (filters.value.species) {
    result = result.filter(pet => pet.species === filters.value.species)
  }

  // 状态筛选
  if (filters.value.status === 'available') {
    result = result.filter(pet => !pet.is_adopted)
  } else if (filters.value.status === 'adopted') {
    result = result.filter(pet => pet.is_adopted)
  }

  return result
})

// 分页后的宠物
const paginatedPets = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredPets.value.slice(start, end)
})

// 防抖搜索
const handleSearch = debounce(() => {
  currentPage.value = 1
}, 300)

// 筛选变化
const handleFilterChange = () => {
  currentPage.value = 1
}

// 重置筛选
const resetFilters = () => {
  searchQuery.value = ''
  filters.value = {
    species: '',
    status: 'all'
  }
  currentPage.value = 1
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page) => {
  currentPage.value = page
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedPets.value = selection
}

// 查看宠物
const viewPet = (petId) => {
  router.push(`/pets/${petId}`)
}

// 编辑宠物
const editPet = (petId) => {
  router.push(`/manage/edit/${petId}`)
}

// 删除宠物
const deletePet = async (pet) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除宠物"${pet.name}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await petsStore.deletePet(pet.id)
    if (result.success) {
      ElMessage.success('删除成功')
    } else {
      ElMessage.error(result.message)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除宠物失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 批量更新状态
const batchUpdateStatus = async (isAdopted) => {
  if (selectedPets.value.length === 0) return

  const action = isAdopted ? '标记为已领养' : '标记为待领养'
  
  try {
    await ElMessageBox.confirm(
      `确定要将选中的 ${selectedPets.value.length} 只宠物${action}吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 这里应该调用批量更新API
    for (const pet of selectedPets.value) {
      await petsStore.updatePet(pet.id, { is_adopted: isAdopted })
    }

    ElMessage.success(`${action}成功`)
    selectedPets.value = []
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量更新失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

// 批量删除
const batchDelete = async () => {
  if (selectedPets.value.length === 0) return

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedPets.value.length} 只宠物吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    for (const pet of selectedPets.value) {
      await petsStore.deletePet(pet.id)
    }

    ElMessage.success('批量删除成功')
    selectedPets.value = []
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 获取宠物数据
const fetchPets = async () => {
  try {
    await petsStore.fetchPets()
  } catch (error) {
    console.error('获取宠物列表失败:', error)
    ElMessage.error('获取宠物列表失败')
  }
}

onMounted(() => {
  fetchPets()
})
</script>

<style scoped>
.pet-manage-container {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.page-title {
  font-size: 2rem;
  color: var(--secondary);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.page-title i {
  color: var(--accent);
}

.page-subtitle {
  color: #666;
  font-size: 1rem;
}

.stats-section {
  margin-bottom: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: var(--card-shadow);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: var(--transition);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.stat-icon.total {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
}

.stat-icon.available {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.stat-icon.adopted {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.stat-icon.applications {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.stat-number {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--secondary);
  margin-bottom: 0.2rem;
}

.stat-label {
  color: #666;
  font-size: 0.9rem;
}

.filter-section {
  margin-bottom: 2rem;
}

.filter-card {
  border-radius: 8px;
  box-shadow: var(--card-shadow);
}

.filter-content {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.search-box {
  flex: 1;
  min-width: 200px;
}

.filters {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.table-section {
  margin-bottom: 2rem;
}

.table-card {
  border-radius: 8px;
  box-shadow: var(--card-shadow);
}

.pet-info-cell {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.pet-avatar-small {
  width: 40px;
  height: 40px;
  background: linear-gradient(45deg, #64b3f4, #c2e59c);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
  flex-shrink: 0;
}

.pet-name {
  font-weight: 500;
  color: var(--secondary);
  margin-bottom: 0.2rem;
}

.pet-breed {
  font-size: 0.8rem;
  color: #666;
}

.action-buttons {
  display: flex;
  gap: 0.3rem;
  flex-wrap: wrap;
}

.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--light);
  border-radius: 6px;
  margin-top: 1rem;
}

.batch-info {
  color: var(--secondary);
  font-weight: 500;
}

.batch-buttons {
  display: flex;
  gap: 0.5rem;
}

.pagination-section {
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .filter-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    min-width: auto;
  }
  
  .batch-actions {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}
</style>
