package com.petadoption.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.petadoption.entity.Pet;
import com.petadoption.mapper.PetMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * MyBatis-Plus宠物服务类
 * 提供基于MyBatis-Plus的宠物数据操作
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MybatisPetService extends ServiceImpl<PetMapper, Pet> {

    private final PetMapper petMapper;

    /**
     * 分页查询可领养的宠物
     */
    public IPage<Pet> getAvailablePetsPage(int pageNum, int pageSize) {
        Page<Pet> page = new Page<>(pageNum, pageSize);
        return petMapper.findAvailablePets(page);
    }

    /**
     * 根据种类分页查询宠物
     */
    public IPage<Pet> getPetsBySpeciesPage(String species, int pageNum, int pageSize) {
        Page<Pet> page = new Page<>(pageNum, pageSize);
        return petMapper.findBySpeciesPage(page, species);
    }

    /**
     * 根据品种分页查询宠物
     */
    public IPage<Pet> getPetsByBreedPage(String breed, int pageNum, int pageSize) {
        Page<Pet> page = new Page<>(pageNum, pageSize);
        return petMapper.findByBreedPage(page, breed);
    }

    /**
     * 根据年龄范围查询宠物
     */
    public IPage<Pet> getPetsByAgeRangePage(Integer minAge, Integer maxAge, int pageNum, int pageSize) {
        Page<Pet> page = new Page<>(pageNum, pageSize);
        return petMapper.findByAgeRange(page, minAge, maxAge);
    }

    /**
     * 查询最新添加的宠物
     */
    public List<Pet> getLatestPets(Integer limit) {
        return petMapper.findLatestAvailablePets(limit);
    }

    /**
     * 查询推荐宠物
     */
    public List<Pet> getRecommendedPets(String species, Long excludeId, Integer limit) {
        return petMapper.findRecommendedPets(species, excludeId, limit);
    }

    /**
     * 统计待领养宠物数量
     */
    public Long countAvailablePets() {
        return petMapper.countAvailablePets();
    }

    /**
     * 统计已领养宠物数量
     */
    public Long countAdoptedPets() {
        return petMapper.countAdoptedPets();
    }

    /**
     * 根据种类统计宠物数量
     */
    public Long countBySpecies(String species) {
        return petMapper.countBySpecies(species);
    }

    /**
     * 复杂查询示例：根据多个条件查询宠物
     */
    public IPage<Pet> searchPets(String species, String breed, Integer minAge, Integer maxAge, 
                                String gender, Boolean isAdopted, int pageNum, int pageSize) {
        Page<Pet> page = new Page<>(pageNum, pageSize);
        QueryWrapper<Pet> queryWrapper = new QueryWrapper<>();
        
        if (species != null && !species.isEmpty()) {
            queryWrapper.eq("species", species);
        }
        if (breed != null && !breed.isEmpty()) {
            queryWrapper.eq("breed", breed);
        }
        if (minAge != null) {
            queryWrapper.ge("age", minAge);
        }
        if (maxAge != null) {
            queryWrapper.le("age", maxAge);
        }
        if (gender != null && !gender.isEmpty()) {
            queryWrapper.eq("gender", gender);
        }
        if (isAdopted != null) {
            queryWrapper.eq("is_adopted", isAdopted);
        }
        
        queryWrapper.orderByDesc("create_time");
        
        return this.page(page, queryWrapper);
    }

    /**
     * 根据关键词搜索宠物（名称、品种、描述）
     */
    public IPage<Pet> searchPetsByKeyword(String keyword, int pageNum, int pageSize) {
        Page<Pet> page = new Page<>(pageNum, pageSize);
        QueryWrapper<Pet> queryWrapper = new QueryWrapper<>();
        
        if (keyword != null && !keyword.isEmpty()) {
            queryWrapper.and(wrapper -> wrapper
                .like("name", keyword)
                .or()
                .like("breed", keyword)
                .or()
                .like("description", keyword)
            );
        }
        
        queryWrapper.eq("is_adopted", false);
        queryWrapper.orderByDesc("create_time");
        
        return this.page(page, queryWrapper);
    }
}
