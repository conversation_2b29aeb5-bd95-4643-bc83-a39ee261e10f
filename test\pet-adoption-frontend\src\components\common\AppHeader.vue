<template>
  <header class="app-header">
    <div class="header-container">
      <!-- Logo -->
      <div class="logo" @click="$router.push('/')">
        <i class="fas fa-paw"></i>
        <span>宠物领养中心</span>
      </div>

      <!-- 导航菜单 -->
      <nav class="nav-menu">
        <router-link to="/" class="nav-item">
          <i class="fas fa-home"></i>
          <span>首页</span>
        </router-link>
        <router-link to="/pets" class="nav-item">
          <i class="fas fa-paw"></i>
          <span>宠物列表</span>
        </router-link>
        <router-link 
          v-if="authStore.isAuthenticated" 
          to="/applications" 
          class="nav-item"
        >
          <i class="fas fa-file-alt"></i>
          <span>我的申请</span>
        </router-link>
        <router-link
          v-if="authStore.isAdmin"
          to="/manage"
          class="nav-item"
        >
          <i class="fas fa-tasks"></i>
          <span>宠物管理</span>
        </router-link>
      </nav>

      <!-- 用户区域 -->
      <div class="user-area">
        <template v-if="authStore.isAuthenticated">
          <!-- 用户信息 -->
          <el-dropdown @command="handleUserCommand">
            <div class="user-info">
              <div class="user-avatar">
                {{ getUserAvatarLetter(authStore.user) }}
              </div>
              <span class="username">{{ authStore.user?.username }}</span>
              <i class="el-icon-arrow-down"></i>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <i class="fas fa-user"></i> 个人资料
                </el-dropdown-item>
                <el-dropdown-item command="applications">
                  <i class="fas fa-file-alt"></i> 我的申请
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <i class="fas fa-sign-out-alt"></i> 退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
        
        <template v-else>
          <!-- 未登录状态 -->
          <div class="auth-buttons">
            <router-link to="/login" class="btn btn-outline">
              <i class="fas fa-sign-in-alt"></i>
              <span>登录</span>
            </router-link>
            <router-link to="/register" class="btn btn-primary">
              <i class="fas fa-user-plus"></i>
              <span>注册</span>
            </router-link>
          </div>
        </template>
      </div>

      <!-- 移动端菜单按钮 -->
      <div class="mobile-menu-btn" @click="toggleMobileMenu">
        <i class="fas fa-bars"></i>
      </div>
    </div>

    <!-- 移动端菜单 -->
    <div class="mobile-menu" :class="{ active: showMobileMenu }">
      <router-link to="/" class="mobile-nav-item" @click="closeMobileMenu">
        <i class="fas fa-home"></i>
        <span>首页</span>
      </router-link>
      <router-link to="/pets" class="mobile-nav-item" @click="closeMobileMenu">
        <i class="fas fa-paw"></i>
        <span>宠物列表</span>
      </router-link>
      <router-link 
        v-if="authStore.isAuthenticated" 
        to="/applications" 
        class="mobile-nav-item"
        @click="closeMobileMenu"
      >
        <i class="fas fa-file-alt"></i>
        <span>我的申请</span>
      </router-link>
      <router-link
        v-if="authStore.isAdmin"
        to="/manage"
        class="mobile-nav-item"
        @click="closeMobileMenu"
      >
        <i class="fas fa-tasks"></i>
        <span>宠物管理</span>
      </router-link>
      
      <div class="mobile-auth-section">
        <template v-if="authStore.isAuthenticated">
          <div class="mobile-user-info">
            <div class="user-avatar">
              {{ getUserAvatarLetter(authStore.user) }}
            </div>
            <span>{{ authStore.user?.username }}</span>
          </div>
          <button class="mobile-logout-btn" @click="handleLogout">
            <i class="fas fa-sign-out-alt"></i>
            <span>退出登录</span>
          </button>
        </template>
        <template v-else>
          <router-link to="/login" class="mobile-auth-btn" @click="closeMobileMenu">
            <i class="fas fa-sign-in-alt"></i>
            <span>登录</span>
          </router-link>
          <router-link to="/register" class="mobile-auth-btn primary" @click="closeMobileMenu">
            <i class="fas fa-user-plus"></i>
            <span>注册</span>
          </router-link>
        </template>
      </div>
    </div>

    <!-- 移动端遮罩 -->
    <div 
      class="mobile-overlay" 
      :class="{ active: showMobileMenu }"
      @click="closeMobileMenu"
    ></div>
  </header>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { getUserAvatarLetter } from '@/utils/auth'

const router = useRouter()
const authStore = useAuthStore()

// 移动端菜单状态
const showMobileMenu = ref(false)

// 切换移动端菜单
const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

// 关闭移动端菜单
const closeMobileMenu = () => {
  showMobileMenu.value = false
}

// 处理用户下拉菜单命令
const handleUserCommand = (command) => {
  switch (command) {
    case 'profile':
      // 跳转到个人资料页面
      router.push('/profile')
      break
    case 'applications':
      router.push('/applications')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 处理登出
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    authStore.logout()
    ElMessage.success('已退出登录')
    router.push('/')
    closeMobileMenu()
  } catch {
    // 用户取消
  }
}
</script>

<style scoped>
.app-header {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
}

.logo i {
  color: var(--accent);
}

.logo:hover {
  opacity: 0.8;
}

.nav-menu {
  display: flex;
  gap: 1.5rem;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.85);
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 0.8rem;
  border-radius: 4px;
  transition: var(--transition);
}

.nav-item:hover,
.nav-item.router-link-active {
  color: white;
  background: rgba(255, 255, 255, 0.15);
}

.user-area {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: var(--transition);
}

.user-info:hover {
  background: rgba(255, 255, 255, 0.1);
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.9rem;
}

.username {
  font-weight: 500;
}

.auth-buttons {
  display: flex;
  gap: 1rem;
}

.mobile-menu-btn {
  display: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: var(--transition);
}

.mobile-menu-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.mobile-menu {
  display: none;
  position: fixed;
  top: 100%;
  left: 0;
  width: 280px;
  height: calc(100vh - 100%);
  background: white;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  z-index: 1001;
  overflow-y: auto;
}

.mobile-menu.active {
  transform: translateX(0);
}

.mobile-nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 1rem 1.5rem;
  color: var(--dark);
  text-decoration: none;
  border-bottom: 1px solid var(--gray);
  transition: var(--transition);
}

.mobile-nav-item:hover,
.mobile-nav-item.router-link-active {
  background: var(--light);
  color: var(--primary);
}

.mobile-auth-section {
  padding: 1.5rem;
  border-top: 1px solid var(--gray);
}

.mobile-user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 1rem;
}

.mobile-logout-btn,
.mobile-auth-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--gray);
  border-radius: 4px;
  background: white;
  color: var(--dark);
  text-decoration: none;
  text-align: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  transition: var(--transition);
  cursor: pointer;
}

.mobile-auth-btn.primary {
  background: var(--accent);
  color: white;
  border-color: var(--accent);
}

.mobile-logout-btn:hover,
.mobile-auth-btn:hover {
  background: var(--light);
}

.mobile-auth-btn.primary:hover {
  background: #e55a57;
}

.mobile-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.mobile-overlay.active {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-container {
    padding: 1rem;
  }
  
  .nav-menu,
  .auth-buttons {
    display: none;
  }
  
  .mobile-menu-btn {
    display: block;
  }
  
  .mobile-menu,
  .mobile-overlay {
    display: block;
  }
}
</style>
