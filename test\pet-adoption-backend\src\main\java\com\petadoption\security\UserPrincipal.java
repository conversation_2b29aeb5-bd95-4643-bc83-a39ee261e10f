package com.petadoption.security;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.petadoption.entity.User;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.Collections;

/**
 * 用户主体类
 * 实现Spring Security的UserDetails接口
 * 包装User实体，提供认证和授权信息
 * 
 * <AUTHOR> Team
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserPrincipal implements UserDetails {
    
    private Long id;
    private String username;
    private String email;
    private String phone;
    private String role;
    private Boolean enabled;
    private Boolean locked;
    
    @JsonIgnore
    private String password;
    
    /**
     * 从User实体创建UserPrincipal
     */
    public static UserPrincipal create(User user) {
        return new UserPrincipal(
                user.getId(),
                user.getUsername(),
                user.getEmail(),
                user.getPhone(),
                user.getRole(),
                user.getEnabled(),
                user.getLocked(),
                user.getPassword()
        );
    }
    
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        // 将角色转换为Spring Security的权限格式
        String authority = "ROLE_" + role.toUpperCase();
        return Collections.singletonList(new SimpleGrantedAuthority(authority));
    }
    
    @Override
    public String getPassword() {
        return password;
    }
    
    @Override
    public String getUsername() {
        return username;
    }
    
    @Override
    public boolean isAccountNonExpired() {
        // 账户永不过期
        return true;
    }
    
    @Override
    public boolean isAccountNonLocked() {
        // 根据locked字段判断账户是否被锁定
        return !locked;
    }
    
    @Override
    public boolean isCredentialsNonExpired() {
        // 凭证永不过期
        return true;
    }
    
    @Override
    public boolean isEnabled() {
        // 根据enabled字段判断账户是否启用
        return enabled;
    }
    
    /**
     * 是否为管理员
     */
    public boolean isAdmin() {
        return "admin".equalsIgnoreCase(role);
    }
    
    /**
     * 是否为普通用户
     */
    public boolean isUser() {
        return "user".equalsIgnoreCase(role);
    }
    
    /**
     * 获取用户ID
     */
    public Long getId() {
        return id;
    }
    
    /**
     * 获取邮箱
     */
    public String getEmail() {
        return email;
    }
    
    /**
     * 获取手机号
     */
    public String getPhone() {
        return phone;
    }
    
    /**
     * 获取角色
     */
    public String getRole() {
        return role;
    }
}
