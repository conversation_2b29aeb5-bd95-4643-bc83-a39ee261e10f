package com.petadoption.controller;

import com.petadoption.common.Result;
import com.petadoption.dto.request.ApplicationCreateRequest;
import com.petadoption.dto.request.ApplicationReviewRequest;
import com.petadoption.dto.response.ApplicationResponse;
import com.petadoption.service.ApplicationService;
import com.petadoption.service.AuthService;
import com.petadoption.util.PageUtil;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

/**
 * 申请控制器
 * 处理领养申请相关的HTTP请求
 * 与前端申请API保持一致
 * 
 * <AUTHOR> Team
 */
@Slf4j
@RestController
@RequestMapping("/api/applications")
@RequiredArgsConstructor
public class ApplicationController {
    
    private final ApplicationService applicationService;
    private final AuthService authService;
    
    /**
     * 创建领养申请
     * 
     * @param request 申请请求
     * @return 申请响应
     */
    @PostMapping
    public Result<ApplicationResponse> createApplication(@Valid @RequestBody ApplicationCreateRequest request) {
        log.info("创建领养申请: 宠物ID {}", request.getPetId());
        
        ApplicationResponse response = applicationService.createApplication(request);
        
        return Result.success("申请提交成功", response);
    }
    
    /**
     * 审核申请（管理员）
     * 
     * @param applicationId 申请ID
     * @param request 审核请求
     * @return 申请响应
     */
    @PutMapping("/{applicationId}/review")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<ApplicationResponse> reviewApplication(
            @PathVariable Long applicationId,
            @Valid @RequestBody ApplicationReviewRequest request
    ) {
        log.info("审核申请: ID {}, 结果: {}", applicationId, request.getStatus());
        
        ApplicationResponse response = applicationService.reviewApplication(applicationId, request);
        
        return Result.success("申请审核完成", response);
    }
    
    /**
     * 取消申请
     * 
     * @param applicationId 申请ID
     * @return 响应结果
     */
    @DeleteMapping("/{applicationId}")
    public Result<Void> cancelApplication(@PathVariable Long applicationId) {
        log.info("取消申请: ID {}", applicationId);
        
        applicationService.cancelApplication(applicationId);
        
        return Result.<Void>success();
    }
    
    /**
     * 获取申请详情
     * 
     * @param applicationId 申请ID
     * @return 申请响应
     */
    @GetMapping("/{applicationId}")
    public Result<ApplicationResponse> getApplicationById(@PathVariable Long applicationId) {
        ApplicationResponse response = applicationService.getApplicationById(applicationId);
        
        return Result.success("获取申请详情成功", response);
    }
    
    /**
     * 获取当前用户的申请列表
     * 
     * @param page 页码
     * @param size 页面大小
     * @param status 状态筛选
     * @return 分页响应
     */
    @GetMapping("/my")
    public Result<PageUtil.PageResponse<ApplicationResponse>> getMyApplications(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String status
    ) {
        Long currentUserId = authService.getCurrentUserId();
        
        PageUtil.PageResponse<ApplicationResponse> response = applicationService.getUserApplications(
                currentUserId, page, size, status);
        
        return Result.success("获取我的申请列表成功", response);
    }
    
    /**
     * 获取指定用户的申请列表（管理员）
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 页面大小
     * @param status 状态筛选
     * @return 分页响应
     */
    @GetMapping("/user/{userId}")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<PageUtil.PageResponse<ApplicationResponse>> getUserApplications(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String status
    ) {
        PageUtil.PageResponse<ApplicationResponse> response = applicationService.getUserApplications(
                userId, page, size, status);
        
        return Result.success("获取用户申请列表成功", response);
    }
    
    /**
     * 获取所有申请列表（管理员）
     * 
     * @param page 页码
     * @param size 页面大小
     * @param status 状态筛选
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 分页响应
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public Result<PageUtil.PageResponse<ApplicationResponse>> getAllApplications(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endDate
    ) {
        PageUtil.PageResponse<ApplicationResponse> response = applicationService.getAllApplications(
                page, size, status, startDate, endDate);
        
        return Result.success("获取申请列表成功", response);
    }
}
